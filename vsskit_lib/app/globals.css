@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import mobile optimizations */
@import '../styles/mobile-optimizations.css';

/* VSSKit Launcher - Complete Styles */
:root {
  /* Base variables */
  --glass-bg: rgba(185, 185, 185, 0.1);
  --glass-border: rgba(255, 254, 254, 0.4);
  --blur-amount: 10px;
  --dock-bg: rgba(31, 31, 31, 0.5);
  --dock-font-color: white;
  --dock-font-size: 21px;
  --dock-item-hover-bg: rgba(51, 49, 49, 0.3);
  --dock-item-active-bg: rgba(38, 38, 38, 0.2);
  --dock-item-padding: 12px;
  --dock-item-radius: 25px;
  --dock-item-icon-size: 32px;
  --dock-border-color: rgba(255, 255, 255, 0.8);
  --dock-z-index: 999;
  --dock-transition: background-color 0.3s ease, transform 0.3s ease;
  --dock-height: 180px;
  --label-spacing: -35px;
  --app-item-padding-bottom: 20px;
  --app-icon-size: 256px;
  --app-icon-size-mobile: 28px;
  --app-icon-hover-scale: 1.5;

  /* Responsive splash screen variables */
  --splash-scale-factor: 1;
  --splash-font-scale: 1;
  --splash-spacing-scale: 1;
  --splash-border-radius: clamp(12px, 2vw, 24px);
  --splash-container-padding: clamp(15px, 3vw, 40px);
  --splash-section-gap: clamp(4px, 1vw, 12px);

  /* Dynamic viewport-based scaling */
  --vw-unit: 1vw;
  --vh-unit: 1vh;
  --vmin-unit: 1vmin;
  --vmax-unit: 1vmax;

  /* Splash screen controls - Consistent scaling */
  --splash-image-scale: 1.0;
  --splash-border-radius-desktop: 24px;
  --splash-border-radius-mobile: clamp(12px, 4vw, 20px);
}

/* Global styles */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  /* background: transparent; REMOVED - was preventing wallpaper visibility */
  color: #ffffff;
  min-height: 100vh;
}

/* Wallpaper support - ensure backgrounds are not overridden */
html {
  background-attachment: fixed !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

body {
  background-attachment: fixed !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* Ensure main containers don't override wallpaper */
main, #__next, [data-nextjs-scroll-focus-boundary] {
  background: transparent !important;
  background-color: transparent !important;
}

/* Force wallpaper visibility - override any conflicting styles */
html[style*="background-image"], body[style*="background-image"] {
  background-attachment: fixed !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
}

/* Remove any Tailwind background classes that might interfere */
.bg-transparent, .bg-black, .bg-white, .bg-gray-900, .bg-slate-900 {
  background: transparent !important;
  background-color: transparent !important;
}

/* Ensure no element covers the wallpaper */
* {
  background-color: transparent;
}

/* Specific overrides for common interfering elements */
div, section, article, aside, nav, header, footer {
  background-color: transparent;
}

* {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Modern Custom Scrollbar - Ultra Rounded Bubble Style */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.4) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.08);
  border-radius: 50px;
  margin: 10px 4px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 -2px 4px rgba(255, 255, 255, 0.1);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.5) 30%,
    rgba(255, 255, 255, 0.45) 70%,
    rgba(255, 255, 255, 0.55) 100%
  );
  border-radius: 50px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background-clip: padding-box;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(0, 0, 0, 0.08),
    0 0 20px rgba(255, 255, 255, 0.2);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.7) 30%,
    rgba(255, 255, 255, 0.65) 70%,
    rgba(255, 255, 255, 0.75) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.2),
    0 3px 12px rgba(0, 0, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.5),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1),
    0 0 30px rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.custom-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.75) 70%,
    rgba(255, 255, 255, 0.85) 100%
  );
  transform: scale(0.9);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 0.6),
    inset 0 -2px 0 rgba(0, 0, 0, 0.15);
}

.custom-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
  border-radius: 50px;
}

/* ZiAssist Frame Scrollbar - Ultra Rounded with Subtle Grey */
.ziassist-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.ziassist-scrollbar::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.ziassist-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.08);
  border-radius: 50px;
  margin: 10px 4px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    inset 0 -2px 4px rgba(255, 255, 255, 0.1);
}

.ziassist-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.5) 30%,
    rgba(255, 255, 255, 0.45) 70%,
    rgba(255, 255, 255, 0.55) 100%
  );
  border-radius: 50px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background-clip: padding-box;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 2px 0 rgba(255, 255, 255, 0.4),
    inset 0 -2px 0 rgba(0, 0, 0, 0.08),
    0 0 20px rgba(255, 255, 255, 0.2);
}

.ziassist-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.7) 30%,
    rgba(255, 255, 255, 0.65) 70%,
    rgba(255, 255, 255, 0.75) 100%
  );
  border: 2px solid rgba(255, 255, 255, 0.4);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.2),
    0 3px 12px rgba(0, 0, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.5),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1),
    0 0 30px rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.ziassist-scrollbar::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 30%,
    rgba(255, 255, 255, 0.75) 70%,
    rgba(255, 255, 255, 0.85) 100%
  );
  transform: scale(0.9);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 0.6),
    inset 0 -2px 0 rgba(0, 0, 0, 0.15);
}

.ziassist-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
  border-radius: 50px;
}

/* TARGETED FLASH PREVENTION - Hide specific UI elements immediately on page load */
body:not(.content-loaded) #main-content {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

body:not(.content-loaded) .top-bar {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

body:not(.content-loaded) .library-view,
body:not(.content-loaded) .demo-view,
body:not(.content-loaded) .ziworkspace,
body:not(.content-loaded) .book-reader,
body:not(.content-loaded) .filter-panel,
body:not(.content-loaded) .window-manager {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Hide content until loaded */
#main-content {
  display: none !important;
  opacity: 0 !important;
  transition: opacity 0.5s ease;
  visibility: hidden !important;
}

/* Show main content when loading is complete */
body.content-loaded #main-content {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Show specific elements when content is loaded */
body.content-loaded .top-bar {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
}

body.content-loaded .library-view,
body.content-loaded .demo-view,
body.content-loaded .ziworkspace,
body.content-loaded .book-reader,
body.content-loaded .filter-panel,
body.content-loaded .window-manager {
  display: initial !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Modern Unique Splash Screen - Smooth blended transition */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: transparent !important;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  opacity: 1;
  visibility: visible;
  transition: opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              visibility 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.splash-screen.fade-out {
  opacity: 0;
  visibility: hidden;
}

.splash-screen.hidden {
  display: none;
}

/* Hide header when splash screen is active */
body:has(.splash-screen:not(.hidden)) .top-bar {
  display: none !important;
}

/* Alternative fallback for browsers that don't support :has() */
.splash-active .top-bar {
  display: none !important;
}

/* Hide all UI elements during splash screen */
.splash-active .library-view,
.splash-active .demo-view,
.splash-active .ziworkspace,
.splash-active .book-reader,
.splash-active .filter-panel,
.splash-active .app-dock,
.splash-active .window-manager,
.splash-active .auth-modal {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Ensure splash screen is completely transparent background */
.splash-screen {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* Smooth blended header and library appearance - seamless startup */
.content-loaded .top-bar {
  display: flex !important;
  opacity: 0;
  animation: headerFadeIn 0.4s ease-out forwards;
}

.content-loaded .library-view {
  display: block !important;
  opacity: 0;
  animation: libraryFadeIn 0.5s ease-out 0.1s forwards;
}

@keyframes headerFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes libraryFadeIn {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.splash-container {
  display: flex;
  width: 900px;
  max-width: 90%;
  height: 520px;
  max-height: 80vh;
  background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
  border-radius: 24px;
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.6);
  overflow: hidden;
  position: relative;
  padding: 0;
  border: 1px solid rgba(194, 185, 185, 0.08);
}

.splash-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: clamp(20px, 4vw, 40px);
  width: 45%;
  box-sizing: border-box;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 24px 0 0 24px;
  position: relative;
  z-index: 2;
}

.splash-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  width: 55%;
  height: 100%;
  border-radius: 0 24px 24px 0;
  padding: 0;
  background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
}

.splash-logo-container::before {
  display: none;
}

.splash-logo-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.splash-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transform: scale(1.0);
  transform-origin: center;
}

/* Removed - using modern app icon styling below */

/* Rotate animation removed - using static logo */

.splash-app-icon-text {
  font-size: 24px;
  font-weight: bold;
  color: #1c1c1c;
}

.splash-app-name {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.splash-copyright {
  font-size: 12px;
  color: #ffffff;
  margin-bottom: 10px;
}

.splash-legal {
  font-size: 12px;
  color: #ffffff;
  margin-bottom: 20px;
}

.splash-status {
  font-size: 12px;
  color: #ffffff;
  margin-bottom: 20px;
}

/* Propagate Loading Animation Container */
.splash-loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 12px 0 24px 0;
  height: 50px;
  width: 100%;
}

/* Removed old progress bar styles - now using ThreeDots component */

/* Removed old ink-filling animation styles - now using ThreeDots component */

/* Removed old ink-filling animation keyframes - now using ThreeDots component */



/* Top Bar Styles - Apple 26 Theme with Uniform Shiny Edge Effect */
.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: rgba(35, 35, 35, 0.95) !important; /* Solid background, no gradient */
  backdrop-filter: blur(25px) saturate(220%) brightness(95%);
  -webkit-backdrop-filter: blur(25px) saturate(220%) brightness(95%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  /* Uniform shiny edge effect - only on edges (reduced brightness) */
  box-shadow:
    0 1px 0 rgba(255, 255, 255, 0.15) inset, /* Top edge shine */
    0 -1px 0 rgba(255, 255, 255, 0.15) inset, /* Bottom edge shine */
    1px 0 0 rgba(255, 255, 255, 0.15) inset, /* Left edge shine */
    -1px 0 0 rgba(255, 255, 255, 0.15) inset, /* Right edge shine */
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.08);
  z-index: 1000;
  display: flex;
  align-items: center;
  padding: 0 16px;
  max-width: 100vw;
  overflow: hidden;
  user-select: none;
}

/* Apple 26 Bottom Shiny Edge - Realistic Light Reflection */
.top-bar::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 15%,
    rgba(255, 255, 255, 0.4) 25%,
    rgba(255, 255, 255, 0.2) 40%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.2) 60%,
    rgba(255, 255, 255, 0.4) 75%,
    rgba(255, 255, 255, 0.8) 85%,
    transparent 100%);
  pointer-events: none;
}

.top-bar::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.launcher-btn {
  width: 40px;
  height: 32px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.launcher-btn:hover {
  background: rgba(133, 132, 132, 0.35);
}

.windows-icon {
  transform: scale(1.27);
  color: rgb(236, 255, 176);
  opacity: 0.9;
  margin-right: 5px;
}

.top-bar-title {
  color: white;
  font-size: 16px;
  opacity: 0.8;
  flex-grow: 1;
  padding-left: 12px;
}

.top-bar .close-btn {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, rgba(255, 59, 48, 0.9), rgba(255, 94, 0, 0.2));
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-right: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.top-bar .close-btn:hover {
  background: linear-gradient(135deg, rgba(255, 48, 48, 1), rgba(255, 59, 48, 0.9));
  transform: scale(1.1);
}

.top-bar .close-btn:active {
  transform: scale(0.95);
}

.top-bar .close-btn .close-icon {
  width: 18px;
  height: 18px;
  color: rgba(255, 255, 255, 1);
  transition: color 0.2s ease;
}

/* Close Confirmation Dialog - Kotobee Style */
.glassmorphism-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.glassmorphism-dialog.show {
  opacity: 1;
  visibility: visible;
}

.glassmorphism-dialog-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    transform: scale(0.9) translateY(-20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.dialog-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-align: center;
}

.dialog-body {
  padding: 24px;
}

.dialog-body p {
  margin: 0 0 24px 0;
  font-size: 16px;
  color: white;
  text-align: center;
  line-height: 1.5;
}

.dialog-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.dialog-btn {
  padding: 10px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.dialog-btn-primary {
  background: rgba(255, 59, 48, 0.8);
  color: white;
  border: 1px solid rgba(255, 59, 48, 0.3);
}

.dialog-btn-primary:hover {
  background: rgba(255, 59, 48, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.3);
}

.dialog-btn:not(.dialog-btn-primary) {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dialog-btn:not(.dialog-btn-primary):hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.dialog-btn:active {
  transform: translateY(0);
}

/* Harmony Taskbar with Subtle 3D and Shiny Glass Refractive Edges */
.harmony-taskbar {
  position: relative;
  overflow: visible;
}

.harmony-taskbar::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 1.5rem;
  background: linear-gradient(135deg,
    rgba(255,255,255,0.08) 0%,
    rgba(255,255,255,0.03) 25%,
    transparent 50%,
    rgba(0,0,0,0.015) 75%,
    rgba(0,0,0,0.03) 100%
  );
  z-index: -1;
  filter: blur(0.15px);
}

.harmony-taskbar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 30%;
  border-radius: 1.5rem 1.5rem 0 0;
  background: linear-gradient(180deg,
    rgba(255,255,255,0.04) 0%,
    rgba(255,255,255,0.015) 50%,
    transparent 100%
  );
  pointer-events: none;
  z-index: 1;
}

/* iOS Taskbar-style App Dock */
.ios-taskbar-dock {
  background: linear-gradient(135deg,
    rgba(40, 40, 40, 0.75) 0%,
    rgba(60, 60, 60, 0.7) 50%,
    rgba(40, 40, 40, 0.75) 100%);
  backdrop-filter: blur(30px) saturate(220%) brightness(120%);
  -webkit-backdrop-filter: blur(30px) saturate(220%) brightness(120%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 32px; /* Much more rounded edges */
  box-shadow:
    0 6px 24px rgba(0, 0, 0, 0.3),
    0 3px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  will-change: transform;
  cursor: grab;
  position: relative;
  padding: 12px 16px; /* Adjusted for larger icons */
  overflow: visible; /* Critical: Allow child elements to scale outside */
  z-index: 1010; /* Ensure proper stacking */
}

.ios-taskbar-dock:active {
  cursor: grabbing;
}

/* AppDock Anti-Clipping Rules */
#app-dock {
  overflow: visible !important;
  z-index: 1010;
}

#app-dock > div {
  overflow: visible !important;
}

/* Ensure all AppDock containers allow overflow */
#app-dock .flex {
  overflow: visible !important;
}

/* Icon containers must allow scaling outside bounds */
#app-dock button {
  overflow: visible !important;
  position: relative;
  z-index: 1;
}

/* Icon wrapper containers */
#app-dock button > div {
  overflow: visible !important;
  position: relative;
}

/* Highest priority for actual icon elements */
#app-dock button > div > div {
  overflow: visible !important;
  position: relative;
  z-index: 20;
}

/* When hovered, ensure maximum z-index */
#app-dock button:hover > div > div {
  z-index: 100 !important;
  overflow: visible !important;
}

/* Aggressive anti-clipping for all AppDock elements */
#app-dock * {
  overflow: visible !important;
}

/* Ensure images can scale beyond their containers */
#app-dock img {
  overflow: visible !important;
  max-width: none !important;
  max-height: none !important;
  position: relative !important;
  z-index: 50 !important;
}

/* Ensure motion divs don't clip */
#app-dock [data-framer-component-type] {
  overflow: visible !important;
}

/* Force all containers to allow overflow */
.ios-taskbar-dock,
.ios-taskbar-dock *,
#app-dock,
#app-dock * {
  overflow: visible !important;
  contain: none !important;
}

/* Specific rules for icon scaling */
#app-dock button {
  position: relative !important;
  overflow: visible !important;
}

#app-dock button > div {
  position: relative !important;
  overflow: visible !important;
}

/* Icon container positioning */
#app-dock button > div > div {
  position: absolute !important;
  overflow: visible !important;
  transform-origin: center !important;
}

/* Ensure hover states work properly */
#app-dock button:hover img,
#app-dock button:hover > div > div {
  transform-origin: center !important;
  z-index: 999 !important;
  overflow: visible !important;
}

/* Critical: Target specific AppDock clipping issues */
#app-dock .w-16.h-16 {
  overflow: visible !important;
  border-radius: 1.5rem !important; /* Maintain rounded appearance */
}

#app-dock .w-12.h-12 {
  overflow: visible !important;
  position: relative !important;
  z-index: 50 !important;
}

/* Ensure the icon background container doesn't clip */
#app-dock button > div.w-16 {
  overflow: visible !important;
  clip-path: none !important;
  mask: none !important;
}

/* Force image containers to allow overflow */
#app-dock .w-12.h-12 > div {
  overflow: visible !important;
}

/* Ensure Next.js Image component doesn't get clipped */
#app-dock img[src] {
  overflow: visible !important;
  clip-path: none !important;
  mask: none !important;
  border-radius: 1rem !important; /* Maintain rounded appearance */
}

/* Critical: Ensure transform scaling works without clipping */
#app-dock button:hover .w-12.h-12 {
  overflow: visible !important;
  z-index: 999 !important;
  transform-origin: center center !important;
}

#app-dock button:hover img {
  overflow: visible !important;
  z-index: 1000 !important;
  transform-origin: center center !important;
}

/* Prevent any CSS containment that might clip */
#app-dock * {
  contain: none !important;
  isolation: auto !important;
}

/* CRITICAL: Fix the specific clipping issue between background and icon */
/* The w-16 h-16 background container must not clip the w-12 h-12 icon container */
#app-dock button > div.w-16.h-16 {
  overflow: visible !important;
  clip: none !important;
  clip-path: none !important;
  mask: none !important;
  border-radius: 1.5rem; /* Keep visual appearance */
}

/* The w-12 h-12 icon container must be able to scale outside its parent */
#app-dock button > div.w-16.h-16 > div.w-12.h-12 {
  overflow: visible !important;
  position: relative !important;
  z-index: 100 !important;
  clip: none !important;
  clip-path: none !important;
  mask: none !important;
}

/* Ensure the image itself can scale */
#app-dock button > div.w-16.h-16 > div.w-12.h-12 img {
  overflow: visible !important;
  clip: none !important;
  clip-path: none !important;
  mask: none !important;
  border-radius: 1rem; /* Keep visual appearance */
}

/* NUCLEAR OPTION: Remove all potential clipping from rounded containers */
#app-dock .rounded-3xl,
#app-dock .rounded-2xl,
#app-dock .rounded-xl {
  overflow: visible !important;
  clip: none !important;
  clip-path: none !important;
  mask: none !important;
}

/* Ensure Tailwind's object-cover doesn't interfere */
#app-dock .object-cover {
  overflow: visible !important;
  object-fit: cover;
}

/* HarmonyTaskbar Anti-Clipping Rules for Notification Dots and Selection Pills */
/* Ensure taskbar buttons allow overflow for notification dots and selection pills */
.harmony-taskbar button,
.harmony-taskbar .relative {
  overflow: visible !important;
  position: relative !important;
}

/* Notification dots must appear outside button frames */
.harmony-taskbar button > div[style*="top: -6px"],
.harmony-taskbar button > div[style*="right: -6px"] {
  overflow: visible !important;
  z-index: 1000 !important;
  position: absolute !important;
}

/* Selection pills must appear outside/below button frames */
.harmony-taskbar button > div[style*="bottom: -8px"] {
  overflow: visible !important;
  z-index: 1000 !important;
  position: absolute !important;
}

/* Ensure taskbar containers don't clip child elements */
.harmony-taskbar .flex,
.harmony-taskbar .space-x-3,
.harmony-taskbar .rounded-2xl {
  overflow: visible !important;
}

/* Specific targeting for motion.div elements */
.harmony-taskbar [data-framer-component-type] {
  overflow: visible !important;
}

/* CRITICAL: Ensure notification dots and selection pills are never clipped */
/* Target notification dots specifically */
.harmony-taskbar motion-div[style*="top: -6px"],
.harmony-taskbar motion-div[style*="right: -6px"] {
  overflow: visible !important;
  clip: none !important;
  clip-path: none !important;
  mask: none !important;
  z-index: 1000 !important;
}

/* Target selection pills specifically */
.harmony-taskbar motion-div[style*="bottom: -8px"] {
  overflow: visible !important;
  clip: none !important;
  clip-path: none !important;
  mask: none !important;
  z-index: 1000 !important;
}

/* Ensure all rounded containers in taskbar allow overflow */
.harmony-taskbar .rounded-2xl,
.harmony-taskbar .rounded-3xl,
.harmony-taskbar .rounded-full {
  overflow: visible !important;
}

/* Force all taskbar elements to allow overflow */
.harmony-taskbar * {
  overflow: visible !important;
  contain: none !important;
}

/* CRITICAL: Absolutely positioned notification dots and selection pills */
/* These elements are positioned outside all containers to prevent clipping */
.fixed.w-4.h-4.rounded-full,
.fixed.rounded-full[style*="width: 32px"] {
  overflow: visible !important;
  clip: none !important;
  clip-path: none !important;
  mask: none !important;
  z-index: 2000 !important;
  position: fixed !important;
  pointer-events: none !important;
}

/* Ensure the main motion container doesn't clip these elements */
.fixed.bottom-0.left-0.right-0.z-50 {
  overflow: visible !important;
  contain: none !important;
}

/* Ensure body and html don't clip fixed elements */
body, html {
  overflow-x: visible !important;
}

/* Subtle Curved Shiny edges that flow with the border radius */
.ios-taskbar-dock::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  height: 1px;
  background:
    radial-gradient(ellipse 120px 32px at 30% -10px, rgba(255, 255, 255, 0.25) 0%, transparent 70%),
    radial-gradient(ellipse 100px 28px at 70% -8px, rgba(255, 255, 255, 0.15) 0%, transparent 65%),
    linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.1) 20%,
      rgba(255, 255, 255, 0.05) 35%,
      rgba(255, 255, 255, 0.02) 50%,
      rgba(255, 255, 255, 0.05) 65%,
      rgba(255, 255, 255, 0.1) 80%,
      transparent 100%);
  border-radius: 31px 31px 0 0;
  pointer-events: none;
  clip-path: inset(0 0 50% 0);
}

.ios-taskbar-dock::after {
  content: '';
  position: absolute;
  bottom: 1px;
  left: 1px;
  right: 1px;
  height: 1px;
  background:
    radial-gradient(ellipse 110px 30px at 25% calc(100% + 8px), rgba(255, 255, 255, 0.15) 0%, transparent 65%),
    radial-gradient(ellipse 90px 26px at 75% calc(100% + 6px), rgba(255, 255, 255, 0.1) 0%, transparent 60%),
    linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.08) 25%,
      rgba(255, 255, 255, 0.03) 40%,
      rgba(255, 255, 255, 0.01) 50%,
      rgba(255, 255, 255, 0.03) 60%,
      rgba(255, 255, 255, 0.08) 75%,
      transparent 100%);
  border-radius: 0 0 31px 31px;
  pointer-events: none;
  clip-path: inset(50% 0 0 0);
}

/* Legacy app-dock class for compatibility */
.app-dock {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1010;
  display: none;
  flex-direction: column;
  align-items: center;
}

.app-dock.show {
  display: flex;
}

.app-dock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}

.app-dock-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.app-dock-close-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.app-dock-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 20px;
  width: 100%;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.app-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.app-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  border-radius: 8px;
  object-fit: cover;
}

.app-label {
  color: white;
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}

/* Window Management Styles */
.popup-window {
  position: absolute;
  display: flex;
  flex-direction: column;
  background: rgba(40, 40, 40, 0.95);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.2);
  min-width: 300px;
  min-height: 200px;
  z-index: 1001;
  overflow: hidden;
  max-width: 100vw;
  max-height: 100vh;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  will-change: transform, width, height, left, top;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.popup-window.resizing,
.popup-window.dragging {
  transition: none !important;
  -webkit-transition: none !important;
}

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(60, 60, 60, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: grab;
}

.window-header:active {
  cursor: grabbing;
}

.window-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

.window-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  margin-right: 6px;
  transition: all 0.2s ease;
}

.close-btn {
  background: #ff5f57;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.minimize-btn {
  background: #febc2e;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.maximize-btn {
  background: #28c840;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.close-btn:hover {
  background: #ff7b74;
}

.minimize-btn:hover {
  background: #fecb55;
}

.maximize-btn:hover {
  background: #4cd659;
}

.window-content {
  flex: 1;
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  overflow: hidden;
  border-radius: 0 0 12px 12px;
}

.window-content iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
}

/* Resize handles */
.resize-handle {
  position: absolute;
  z-index: 1002;
  background: transparent;
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.resize-pill {
  position: absolute;
  background: rgba(100, 255, 100, 0.6);
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  pointer-events: none;
  box-shadow: 0 0 4px rgba(100, 255, 100, 0.3);
}

.popup-window.resizing .resize-pill {
  opacity: 1;
  background: #00ff00;
  box-shadow: 0 0 10px 3px rgba(50, 255, 50, 0.8);
}

.resize-handle.n { top: -5px; left: 0; right: 0; height: 10px; cursor: n-resize; }
.resize-handle.s { bottom: -5px; left: 0; right: 0; height: 10px; cursor: s-resize; }
.resize-handle.e { top: 0; right: -5px; bottom: 0; width: 10px; cursor: e-resize; }
.resize-handle.w { top: 0; left: -5px; bottom: 0; width: 10px; cursor: w-resize; }
.resize-handle.ne { top: -5px; right: -5px; width: 15px; height: 15px; cursor: ne-resize; }
.resize-handle.nw { top: -5px; left: -5px; width: 15px; height: 15px; cursor: nw-resize; }
.resize-handle.se { bottom: -5px; right: -5px; width: 15px; height: 15px; cursor: se-resize; }
.resize-handle.sw { bottom: -5px; left: -5px; width: 15px; height: 15px; cursor: sw-resize; }

/* ZiApp Background */
.ziapp-background {
  position: fixed;
  top: 32px;
  left: 0;
  width: 100%;
  height: calc(100vh - 32px);
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 50%, rgba(51, 65, 85, 0.85) 100%);
  z-index: 800;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ziapp-background.visible {
  display: block;
  opacity: 1;
}

/* Library View Styles */
.library-view {
  position: fixed;
  top: 40px;
  left: 0;
  width: 100%;
  height: calc(100vh - 40px);
  background: #f8f9fa;
  z-index: 900;
  overflow-y: auto;
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.library-view.visible {
  display: block;
  opacity: 1;
}

.library-view.hidden {
  display: none;
}

/* Professional Book Grid - Standard Book Proportions */
.book-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 18px;
  padding: 20px;
  max-width: 100%;
}

/* Responsive grid breakpoints for standard book proportions */
@media (max-width: 480px) {
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 14px;
    padding: 16px;
  }
}

@media (min-width: 640px) {
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(145px, 1fr));
    gap: 20px;
  }
}

@media (min-width: 768px) {
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 22px;
  }
}

@media (min-width: 1024px) {
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(155px, 1fr));
    gap: 24px;
  }
}

@media (min-width: 1280px) {
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 26px;
  }
}

@media (min-width: 1536px) {
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(165px, 1fr));
    gap: 28px;
  }
}

.book-card {
  background: white;
  border-radius: 10px;
  overflow: visible; /* Allow book cover to show 3D effects */
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
  padding: 8px;
  display: flex;
  flex-direction: column;
}

.book-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1), 0 3px 8px rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.06);
}

.book-card:hover .book-cover {
  transform: scale(1.015);
  box-shadow:
    0 3px 10px rgba(0, 0, 0, 0.12),
    0 2px 5px rgba(0, 0, 0, 0.08),
    inset 0 0 0 1px rgba(255, 255, 255, 0.15);
}

/* Standard Book Proportions - Professional Kotobee Style */
.book-cover {
  width: 100%;
  height: 180px;
  object-fit: cover;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 6px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.08),
    0 1px 2px rgba(0, 0, 0, 0.04),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  position: relative;
  aspect-ratio: 3/4; /* Standard book ratio */
}

/* Book spine effect */
.book-cover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to right,
    rgba(0, 0, 0, 0.12) 0%,
    rgba(0, 0, 0, 0.04) 50%,
    transparent 100%);
  border-radius: 6px 0 0 6px;
  z-index: 1;
}

/* Book highlight effect */
.book-cover::after {
  content: '';
  position: absolute;
  top: 4px;
  right: 4px;
  width: calc(100% - 16px);
  height: 18px;
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 0.25) 0%,
    transparent 100%);
  border-radius: 4px 4px 0 0;
  z-index: 1;
}

/* Responsive cover heights - standard book proportions */
@media (max-width: 640px) {
  .book-cover {
    height: 160px;
  }
}

@media (min-width: 768px) {
  .book-cover {
    height: 190px;
  }
}

@media (min-width: 1024px) {
  .book-cover {
    height: 200px;
  }
}

@media (min-width: 1280px) {
  .book-cover {
    height: 210px;
  }
}

.book-info {
  padding: 8px 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-height: 85px;
  flex: 1;
}

.book-title {
  font-size: 13px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 34px; /* Ensure consistent height for 2 lines */
}

.book-author {
  font-size: 11px;
  color: #666;
  margin: 0;
  font-weight: 400;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  opacity: 0.8;
  min-height: 14px; /* Prevent layout shift */
}

.book-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: auto;
  min-height: 28px; /* Ensure space for both grade and subject */
}

.book-grade {
  font-size: 10px;
  color: #888;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  opacity: 0.7;
  line-height: 1.2;
}

.book-subject {
  font-size: 10px;
  color: #0066cc;
  font-weight: 500;
  background: rgba(0, 102, 204, 0.08);
  padding: 3px 8px;
  border-radius: 12px;
  align-self: flex-start;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid rgba(0, 102, 204, 0.15);
  line-height: 1.2;
}

/* Enhanced book cover realism */
.book-cover:hover {
  transform: scale(1.02) rotateY(-2deg);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    0 3px 8px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.2),
    -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Responsive text sizes - standard book proportions */
@media (min-width: 768px) {
  .book-title {
    font-size: 14px;
    min-height: 36px;
  }

  .book-author {
    font-size: 12px;
    min-height: 15px;
  }

  .book-grade,
  .book-subject {
    font-size: 11px;
  }

  .book-info {
    min-height: 90px;
  }
}

@media (min-width: 1024px) {
  .book-info {
    padding: 10px 0 0 0;
    min-height: 95px;
  }

  .book-title {
    font-size: 14px;
    min-height: 37px;
  }

  .book-author {
    font-size: 12px;
    min-height: 16px;
  }

  .book-card {
    padding: 10px;
  }

  .book-meta {
    min-height: 32px;
  }
}

@media (min-width: 1280px) {
  .book-title {
    font-size: 15px;
    min-height: 39px;
  }

  .book-author {
    font-size: 13px;
    min-height: 17px;
  }

  .book-info {
    min-height: 100px;
  }
}

/* Additional book styling for premium look */
.book-card {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.book-card:hover {
  background: #fafafa;
}

/* Search styles */
.vsskit-search-container {
  max-width: 600px;
  width: 100%;
}

.vsskit-search-input {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.vsskit-search-input:focus-within {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.vsskit-search-field {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 20px;
  font-size: 16px;
  background: transparent;
  color: #333;
}

.vsskit-search-field::placeholder {
  color: #999;
}

.vsskit-search-divider {
  width: 1px;
  height: 24px;
  background: #e0e0e0;
  margin: 0 8px;
}

.vsskit-search-btn {
  background: none;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 0 24px 24px 0;
  transition: background-color 0.2s ease;
}

.vsskit-search-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.vsskit-filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.vsskit-filter-btn:hover {
  background: #f8f9fa;
  border-color: #ccc;
}

.vsskit-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, #e0e0e0, transparent);
  margin: 20px 0;
}

/* Authentication Modal Styles */
.glassmorphism-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.glassmorphism-dialog.show {
  opacity: 1;
  visibility: visible;
}

.glassmorphism-dialog-content {
  background: rgba(224, 224, 224, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 0;
  color: white;
  width: 90%;
  max-width: 400px;
  text-align: center;
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  transform-origin: center;
}

.glassmorphism-dialog.show .glassmorphism-dialog-content {
  transform: scale(1);
}

.dialog-header {
  background: rgba(59, 68, 38, 0.25);
  padding: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.dialog-body {
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.9);
}

.dialog-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.dialog-btn {
  background: rgba(85, 84, 84, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 5px;
  padding: 10px 20px;
  color: rgb(82, 87, 71);
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.dialog-btn:hover {
  background: rgba(95, 124, 158, 0.35);
}

.dialog-btn-primary {
  background: rgba(183, 255, 68, 0.685);
}

.dialog-btn-primary:hover {
  background: rgba(82, 152, 243, 0.692);
  color: rgb(255, 255, 255);
}

/* Filter Panel Styles - Left Sidebar with Push Animation */
.filter-panel-overlay {
  position: fixed;
  top: 32px;
  left: 0;
  width: 100%;
  height: calc(100vh - 32px);
  background: rgba(0, 0, 0, 0.2);
  z-index: 1500;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), visibility 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.filter-panel-overlay.show {
  opacity: 1;
  visibility: visible;
}

.filter-panel {
  position: fixed;
  top: 32px;
  left: 0;
  width: 320px;
  height: calc(100vh - 32px);
  background: white;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1), 4px 0 40px rgba(0, 0, 0, 0.05);
  transform: translateX(-100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1501;
  overflow-y: auto;
  border-right: 1px solid #e5e7eb;
  will-change: transform;
}

.filter-panel.show {
  transform: translateX(0);
}

/* Library content with synchronized push animation */
.library-view {
  position: fixed;
  top: 40px;
  left: 0;
  width: 100%;
  height: calc(100vh - 40px);
  background: #f8f9fa;
  z-index: 900;
  overflow-y: auto;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: none;
  opacity: 0;
  will-change: transform, width;
}

.library-view.visible {
  display: block;
  opacity: 1;
  transition: opacity 0.3s ease, transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94), width 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Desktop: Push content to the right */
.library-view.sidebar-open {
  transform: translateX(320px);
  width: calc(100% - 320px);
}

/* Tablet: Smaller sidebar, content still pushes */
@media (max-width: 1024px) and (min-width: 769px) {
  .filter-panel {
    width: 280px;
  }

  .library-view.sidebar-open {
    transform: translateX(280px);
    width: calc(100% - 280px);
  }
}

/* Mobile: Full overlay behavior */
@media (max-width: 768px) {
  .filter-panel {
    width: 85%;
    max-width: 320px;
  }

  .library-view.sidebar-open {
    transform: translateX(85%);
    width: 100%;
  }

  .filter-panel-overlay.show {
    background: rgba(0, 0, 0, 0.4);
  }
}

/* Small mobile: Full width sidebar */
@media (max-width: 480px) {
  .filter-panel {
    width: 100%;
  }

  .library-view.sidebar-open {
    transform: translateX(100%);
    width: 100%;
  }
}

/* Enhanced animations for smooth performance */
.filter-panel,
.library-view {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000px;
  -webkit-perspective: 1000px;
}

/* Smooth scroll behavior when sidebar is open */
.library-view.sidebar-open {
  overflow-x: hidden;
}

/* Prevent horizontal scrolling during animation */
body.sidebar-animating {
  overflow-x: hidden;
}

/* Enhanced visual feedback during animation */
.filter-panel.show {
  transform: translateX(0);
  box-shadow: 2px 0 25px rgba(0, 0, 0, 0.12), 6px 0 50px rgba(0, 0, 0, 0.08);
}

/* Smooth content scaling during animation */
.library-view.sidebar-open {
  transform: translateX(320px);
  width: calc(100% - 320px);
}

/* Add subtle bounce effect for premium feel */
@keyframes slideInBounce {
  0% {
    transform: translateX(-100%);
  }
  70% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes slideOutBounce {
  0% {
    transform: translateX(0);
  }
  30% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Apply bounce animations for premium feel */
.filter-panel.show {
  animation: slideInBounce 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.filter-panel:not(.show) {
  animation: slideOutBounce 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Synchronized library content animation */
@keyframes contentPushIn {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(320px);
  }
}

@keyframes contentPushOut {
  0% {
    transform: translateX(320px);
  }
  100% {
    transform: translateX(0);
  }
}

/* Apply content animations */
.library-view.sidebar-open {
  animation: contentPushIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.library-view:not(.sidebar-open) {
  animation: contentPushOut 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Disable animations on mobile for better performance */
@media (max-width: 768px) {
  .filter-panel.show,
  .filter-panel:not(.show),
  .library-view.sidebar-open,
  .library-view:not(.sidebar-open) {
    animation: none;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;

    /* Custom CSS Variables for Splash Screen */
    --splash-image-scale: 1.35;
    --splash-image-x-offset: -5%;
    --splash-image-y-offset: 0%;
    --splash-wrapper-width: 120%;
    --splash-wrapper-height: 120%;
    --splash-margin-top: -10%;
    --splash-margin-right: -5%;
    --splash-margin-bottom: -10%;
    --splash-margin-left: -15%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Glassmorphism utility classes */
  .glassmorphism {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glassmorphism-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }

  /* Liquid Glass Interaction Effects */
  .liquid-glass {
    position: relative;
    display: flex;
    font-weight: 600;
    overflow: hidden;
    cursor: pointer;
    isolation: isolate;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2);
  }

  .liquid-glass-advanced {
    box-shadow: 0 6px 6px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 0, 0, 0.1);
  }

  .liquid-glass-fallback {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
  }

  .liquid-glass-small {
    border-radius: 12px;
  }

  .liquid-glass-medium {
    border-radius: 16px;
  }

  .liquid-glass-large {
    border-radius: 24px;
  }

  .liquid-glass-effect {
    position: absolute;
    z-index: 0;
    inset: 0;
    backdrop-filter: blur(3px);
    overflow: hidden;
    isolation: isolate;
  }

  .liquid-glass-tint {
    z-index: 1;
    position: absolute;
    inset: 0;
  }

  .liquid-glass-shine {
    position: absolute;
    inset: 0;
    z-index: 2;
    overflow: hidden;
  }

  .liquid-glass-content {
    z-index: 3;
    position: relative;
  }

  /* Performance optimizations for low-end devices */
  @media (max-width: 768px), (max-height: 600px) {
    .liquid-glass-advanced {
      transform: none !important;
      filter: none !important;
      backdrop-filter: blur(5px) !important;
    }

    .liquid-glass-effect {
      display: none;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .liquid-glass {
      transition: none;
    }

    .liquid-glass-effect,
    .liquid-glass-tint,
    .liquid-glass-shine {
      transition: none;
    }
  }

  /* Enhanced smoothing for perfect glass effect */
  .liquid-glass,
  .liquid-glass *,
  [class*="liquid-glass"] {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px;
    will-change: transform;
    isolation: isolate;
  }

  /* Smooth edges for all rounded elements */
  [class*="rounded"],
  .rounded,
  .rounded-full,
  .rounded-xl,
  .rounded-2xl,
  .rounded-3xl {
    -webkit-mask-image: -webkit-radial-gradient(white, black);
    mask-image: radial-gradient(white, black);
  }

  /* Enhanced backdrop filter support */
  @supports (backdrop-filter: blur(1px)) {
    .backdrop-blur-sm,
    .backdrop-blur,
    .backdrop-blur-md,
    .backdrop-blur-lg,
    .backdrop-blur-xl,
    .backdrop-blur-2xl,
    .backdrop-blur-3xl {
      -webkit-backdrop-filter: inherit;
    }
  }

  /* Smooth motion for all interactive elements */
  button,
  [role="button"],
  .cursor-pointer {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Perfect circle smoothing */
  .rounded-full {
    border-radius: 50% !important;
  }

  /* Professional splash screen styles - Clean consistent design */
  .splash-container {
    @apply relative flex flex-row items-center overflow-hidden;
    width: clamp(300px, 90vw, 900px);
    height: clamp(200px, 80vh, 520px);
    background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
    border-radius: 24px;
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(194, 185, 185, 0.08);
  }

  .splash-info {
    @apply flex flex-col items-start w-[45%] box-border relative;
    padding: clamp(20px, 4vw, 40px);
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    border-radius: 24px 0 0 24px;
    z-index: 2;
  }

  .splash-logo-container {
    @apply flex justify-center items-center flex-1 flex-col relative overflow-hidden w-[55%] h-full p-0;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border-radius: 0 24px 24px 0;
  }

  .splash-logo-container::before {
    display: none;
  }

  .splash-logo {
    @apply w-full h-full block m-0 p-0 relative;
    object-fit: cover;
    object-position: center center;
    transform: scale(1.0);
    transform-origin: center center;
    z-index: 1;
  }

  /* iOS Taskbar-style App Dock */
  .ios-taskbar-dock {
    background: linear-gradient(135deg,
      rgba(40, 40, 40, 0.75) 0%,
      rgba(60, 60, 60, 0.7) 50%,
      rgba(40, 40, 40, 0.75) 100%);
    backdrop-filter: blur(30px) saturate(220%) brightness(120%);
    -webkit-backdrop-filter: blur(30px) saturate(220%) brightness(120%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 32px;
    box-shadow:
      0 6px 24px rgba(0, 0, 0, 0.3),
      0 3px 12px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    will-change: transform;
    cursor: grab;
    position: relative;
    padding: 12px 16px;
    overflow: visible !important; /* Critical: Allow child elements to scale outside */
    z-index: 1010; /* Ensure proper stacking */
  }

  .ios-taskbar-dock:active {
    cursor: grabbing;
  }

  /* Responsive AppDock Anti-Clipping Rules */
  #app-dock {
    overflow: visible !important;
  }

  #app-dock > div,
  #app-dock .flex,
  #app-dock button,
  #app-dock button > div,
  #app-dock button > div > div {
    overflow: visible !important;
  }

  /* Aggressive responsive anti-clipping */
  #app-dock *,
  .ios-taskbar-dock * {
    overflow: visible !important;
    contain: none !important;
  }

  #app-dock img {
    overflow: visible !important;
    max-width: none !important;
    max-height: none !important;
    z-index: 999 !important;
  }

  /* Responsive HarmonyTaskbar with Subtle 3D and Shiny Glass Effects */
  .harmony-taskbar {
    position: relative;
    overflow: visible;
  }

  .harmony-taskbar::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 1.5rem;
    background: linear-gradient(135deg,
      rgba(255,255,255,0.1) 0%,
      rgba(255,255,255,0.03) 25%,
      transparent 50%,
      rgba(0,0,0,0.02) 75%,
      rgba(0,0,0,0.03) 100%
    );
    z-index: -1;
    filter: blur(0.15px);
  }

  .harmony-taskbar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30%;
    border-radius: 1.5rem 1.5rem 0 0;
    background: linear-gradient(180deg,
      rgba(255,255,255,0.05) 0%,
      rgba(255,255,255,0.015) 50%,
      transparent 100%
    );
    pointer-events: none;
    z-index: 1;
  }

  /* Responsive HarmonyTaskbar Anti-Clipping Rules */
  .harmony-taskbar * {
    overflow: visible !important;
    contain: none !important;
  }

  .harmony-taskbar button,
  .harmony-taskbar .relative,
  .harmony-taskbar .rounded-2xl,
  .harmony-taskbar .rounded-3xl {
    overflow: visible !important;
  }

  /* Ensure notification dots and selection pills work on mobile */
  .harmony-taskbar motion-div[style*="top: -6px"],
  .harmony-taskbar motion-div[style*="right: -6px"],
  .harmony-taskbar motion-div[style*="bottom: -8px"] {
    overflow: visible !important;
    z-index: 1000 !important;
  }

  /* Responsive absolutely positioned elements */
  .fixed.w-4.h-4.rounded-full,
  .fixed.rounded-full[style*="width: 32px"] {
    overflow: visible !important;
    z-index: 2000 !important;
    position: fixed !important;
  }

  /* Ensure mobile doesn't clip fixed elements */
  body, html {
    overflow-x: visible !important;
  }

  /* Subtle Curved Shiny edges that flow with the border radius */
  .ios-taskbar-dock::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    height: 1px;
    background:
      radial-gradient(ellipse 120px 32px at 30% -10px, rgba(255, 255, 255, 0.25) 0%, transparent 70%),
      radial-gradient(ellipse 100px 28px at 70% -8px, rgba(255, 255, 255, 0.15) 0%, transparent 65%),
      linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.1) 20%,
        rgba(255, 255, 255, 0.05) 35%,
        rgba(255, 255, 255, 0.02) 50%,
        rgba(255, 255, 255, 0.05) 65%,
        rgba(255, 255, 255, 0.1) 80%,
        transparent 100%);
    border-radius: 31px 31px 0 0;
    pointer-events: none;
    clip-path: inset(0 0 50% 0);
  }

  .ios-taskbar-dock::after {
    content: '';
    position: absolute;
    bottom: 1px;
    left: 1px;
    right: 1px;
    height: 1px;
    background:
      radial-gradient(ellipse 110px 30px at 25% calc(100% + 8px), rgba(255, 255, 255, 0.15) 0%, transparent 65%),
      radial-gradient(ellipse 90px 26px at 75% calc(100% + 6px), rgba(255, 255, 255, 0.1) 0%, transparent 60%),
      linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.08) 25%,
        rgba(255, 255, 255, 0.03) 40%,
        rgba(255, 255, 255, 0.01) 50%,
        rgba(255, 255, 255, 0.03) 60%,
        rgba(255, 255, 255, 0.08) 75%,
        transparent 100%);
    border-radius: 0 0 31px 31px;
    pointer-events: none;
    clip-path: inset(50% 0 0 0);
  }

  /* Legacy app dock styles */
  .app-dock {
    @apply fixed z-[1010] flex flex-col items-center;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
  }

  .app-dock.show {
    @apply block;
  }

  /* Window management styles */
  .popup-window {
    @apply absolute flex flex-col bg-transparent border border-white/20 rounded-xl shadow-2xl min-w-[300px] min-h-[200px] z-[1001] overflow-hidden max-w-full max-h-full;
    transform: translateZ(0);
    backface-visibility: hidden;
    will-change: transform, width, height, left, top;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  }

  .popup-window.resizing,
  .popup-window.dragging {
    @apply transition-none;
  }

  /* Resize handles */
  .resize-handle {
    @apply absolute z-[1002] bg-transparent touch-none select-none opacity-0 transition-opacity duration-150;
  }

  .popup-window:hover .resize-handle {
    @apply opacity-100;
  }

  .resize-pill {
    @apply absolute bg-green-400/60 rounded opacity-0 transition-all duration-200 pointer-events-none;
    box-shadow: 0 0 4px rgba(100, 255, 100, 0.3);
  }

  .popup-window.resizing .resize-pill {
    @apply opacity-100 bg-green-400;
    box-shadow: 0 0 10px 3px rgba(50, 255, 50, 0.8);
  }

  /* Top bar styles */
  .top-bar {
    @apply fixed top-0 left-0 right-0 h-10 z-[1000] flex items-center px-4 w-full overflow-x-hidden;
    background: rgba(35, 35, 35, 0.95); /* Solid background, no gradient */
    backdrop-filter: blur(25px) saturate(220%) brightness(95%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    /* Uniform shiny edge effect - only on edges (reduced brightness) */
    box-shadow:
      0 1px 0 rgba(255, 255, 255, 0.15) inset, /* Top edge shine */
      0 -1px 0 rgba(255, 255, 255, 0.15) inset, /* Bottom edge shine */
      1px 0 0 rgba(255, 255, 255, 0.15) inset, /* Left edge shine */
      -1px 0 0 rgba(255, 255, 255, 0.15) inset, /* Right edge shine */
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.08);
  }

  /* Apple 26 Bottom Shiny Edge - Realistic Light Reflection */
  .top-bar::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(255, 255, 255, 0.8) 15%,
      rgba(255, 255, 255, 0.4) 25%,
      rgba(255, 255, 255, 0.2) 40%,
      rgba(255, 255, 255, 0.15) 50%,
      rgba(255, 255, 255, 0.2) 60%,
      rgba(255, 255, 255, 0.4) 75%,
      rgba(255, 255, 255, 0.8) 85%,
      transparent 100%);
    pointer-events: none;
  }

  /* Book reader styles */
  .epub-frame {
    @apply fixed left-0 w-full border-none shadow-none overflow-hidden object-fill opacity-0 z-[850];
    display: none;
    visibility: hidden;
    top: 40px;
    height: calc(100vh - 40px);
  }

  .epub-frame.visible {
    @apply block opacity-100 z-[950];
  visibility: visible;
  }

  /* Library view styles */
  .library-view {
    @apply fixed left-0 w-full z-[900] bg-gradient-to-br from-gray-900 to-gray-800 overflow-y-auto;
    top: 40px;
    height: calc(100vh - 40px);
  }

  .library-view.hidden {
    display: none;
  }

  /* VSSKit Library Search Styles - Google-like design */
  .vsskit-search-container {
    @apply relative flex items-center;
  }

  .vsskit-search-input {
    @apply relative flex items-center bg-white rounded-full border border-gray-200 transition-all duration-200;
    width: 580px;
    height: 46px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .vsskit-search-input:hover {
    @apply shadow-md;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .vsskit-search-input:focus-within {
    @apply shadow-lg border-transparent;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }

  .vsskit-search-field {
    @apply flex-1 px-12 py-3 bg-transparent border-none outline-none text-gray-900 placeholder:text-gray-500 text-base;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  .vsskit-voice-btn {
    @apply absolute right-3 p-2 hover:bg-gray-100 rounded-full transition-colors duration-150;
  }

  .vsskit-filter-btn {
    @apply p-3 text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-gray-200 rounded-full transition-all duration-200;
    height: 46px;
    width: 46px;
  }

  /* Filter panel styles - Updated for slide-in from left */
  .filter-panel-overlay {
    @apply fixed inset-0 bg-black/20 z-[999];
  }

  .filter-panel-container {
    @apply fixed left-0 top-0 bottom-0 w-80 bg-white border-r border-gray-200 shadow-lg transform transition-transform duration-300 ease-in-out z-[1000] overflow-y-auto;
  }

  .filter-panel-open .filter-panel-container {
    @apply translate-x-0;
  }

  .filter-panel-closed .filter-panel-container {
    @apply -translate-x-full;
  }

  /* Remove duplicate book grid styles - using main styles above */

  /* Authentication modal styles */
  .auth-modal {
    @apply fixed inset-0 z-[10000] flex items-center justify-center bg-black/50 opacity-0 invisible transition-all duration-300;
  }

  .auth-modal.show {
    @apply opacity-100 visible;
  }

  .modal-content {
    @apply glassmorphism-dark rounded-lg p-0 text-white w-[90%] max-w-md text-center transform scale-0 transition-transform duration-300 overflow-hidden;
    transform-origin: center;
  }

  .auth-modal.show .modal-content {
    @apply scale-100;
  }

  /* Utility classes */
  .ziapp-background {
    @apply fixed inset-0 bg-white z-[800] hidden;
    top: 32px;
  }

  .ziapp-background.visible {
    @apply block;
  }

  /* Desktop layout - Clean modern design without diagonal elements */
  @media (min-width: 769px) {
    .splash-info {
      position: relative !important;
      width: 45% !important;
      height: 100% !important;
      padding: clamp(35px, 4vw, 50px) clamp(30px, 3vw, 40px) !important;
      display: flex !important;
      flex-direction: column !important;
      justify-content: flex-start !important;
      background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%) !important;
      border-radius: 24px 0 0 24px !important;
      z-index: 2 !important;
      box-shadow: 8px 0 24px rgba(0, 0, 0, 0.1) !important;
    }

    .splash-logo-container {
      position: relative !important;
      width: 55% !important;
      height: 100% !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      overflow: hidden !important;
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%) !important;
      border-radius: 0 24px 24px 0 !important;
    }

    .splash-logo-container::before {
      display: none !important;
    }

    .splash-logo-wrapper {
      width: 100% !important;
      height: 100% !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      position: relative !important;
      z-index: 1 !important;
      overflow: hidden !important;
    }

    .splash-logo {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover !important;
      object-position: center !important;
      transform: scale(1.0) !important;
      transform-origin: center !important;
    }
  }

  /* Modern app icon with refined styling - Static without background */
  .splash-app-icon {
    width: clamp(50px, 8vw, 65px);
    height: clamp(50px, 8vw, 65px);
    background: transparent;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: clamp(20px, 4vw, 28px);
    /* Removed animation for static display */
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
    padding: 8px;
  }

  /* Rotate animation removed - static logo only */

  /* App icon text - Modern refined styling */
  .splash-app-icon-text {
    font-size: clamp(22px, 4vw, 30px);
    font-weight: 700;
    color: #ffffff;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* App icon image - Modern refined styling without background */
  .splash-app-icon-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.25));
    border-radius: 4px;
  }

  /* App name - Modern typography with enhanced hierarchy */
  .splash-app-name {
    font-size: clamp(24px, 4vw, 36px);
    font-weight: 700;
    color: #1a1a1a !important;
    margin: 0 0 12px 0;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: -0.8px;
    line-height: 1.1;
    text-rendering: optimizeLegibility;
  }

  /* VSSKit Library subtitle - Modern refined styling with dark purple close to black */
  .splash-library-info {
    font-size: clamp(14px, 2.5vw, 18px);
    color: #1e1b4b !important;
    margin: 0 0 16px 0;
    font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
    letter-spacing: 0.3px;
    opacity: 0.95;
    text-rendering: optimizeLegibility;
  }

  /* Description text - Modern clean styling with harmonious grey and pronounced underline */
  .splash-description {
    font-size: clamp(16px, 3vw, 20px);
    color: #64748b !important;
    margin: 0 0 28px 0;
    font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    letter-spacing: 0.2px;
    text-rendering: optimizeLegibility;
    position: relative;
    padding-bottom: 12px;
  }

  /* Subtle centralized underline with darker nature-inspired gradient */
  .splash-description::after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%; /* Extended width for elongated tapering */
    height: 1px; /* Subtle thickness */
    background: linear-gradient(90deg, transparent 0%, rgba(107, 142, 35, 0.15) 25%, rgba(46, 139, 87, 0.35) 45%, rgba(184, 134, 11, 0.4) 50%, rgba(46, 139, 87, 0.35) 55%, rgba(107, 142, 35, 0.15) 75%, transparent 100%);
    border-radius: 0.5px;
  }



  /* Legal text - Modern refined styling */
  .splash-legal {
    font-size: clamp(12px, 2vw, 14px);
    color: #718096 !important;
    margin: 0 0 40px 0;
    line-height: 1.7;
    font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    letter-spacing: 0.1px;
    max-width: 280px;
  }

  .splash-legal .vssk {
    color: #2e8b57 !important;
  }

  .splash-legal .ziapp {
    color: #b8860b !important;
  }

  /* Photoshop-style loading text - Lighter nature-inspired styling */
  .splash-loading-text {
    color: #8fbc8f !important;
    font-size: clamp(10px, 1.8vw, 12px);
    margin: 0 0 6px 0;
    font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 500;
    letter-spacing: 0.4px;
    text-align: center;
    opacity: 0.9;
    text-transform: none;
    transition: opacity 0.3s ease;
  }

  /* Using original progress bar styling above */

  /* Using original progress bar styling above */

  /* Using original progress bar styling above */

  /* Bottom branding area with enhanced styling */
  .splash-branding {
    margin-top: auto;
    padding-top: 8px;
    display: flex;
    align-items: center;
  }

  /* Custom Branding Logo - Updated for image */
  .splash-branding-icon {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    object-fit: contain;
    object-position: center;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.2));
    border-radius: 4px; /* Slight rounding for professional look */
  }

  .splash-branding-text {
    font-size: 14px;
    color: #555555 !important;
    font-weight: 500;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* Decorative elements - warm nature-inspired background decoration */
  .splash-decoration {
    position: absolute;
    width: 350px;
    height: 350px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(154, 205, 50, 0.08) 0%, rgba(143, 188, 143, 0.06) 40%, rgba(218, 165, 32, 0.04) 70%, transparent 100%);
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    z-index: 1;
    filter: blur(3px);
  }

  /* Main Feature Image - Consistent scaling across all screen sizes */
  .splash-logo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
    transform: scale(1.0);
    transform-origin: center center;
    transition: transform 0.3s ease;
  }

  /* Remove floating animation - keep for potential future use but don't apply */
  @keyframes float {
    0% { transform: translateY(0px) scale(1.5); }
    50% { transform: translateY(-15px) scale(1.52); }
    100% { transform: translateY(0px) scale(1.5); }
  }

  /* Cog wheels for decoration */
  .cog-wheel {
    position: absolute;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 3;
    display: none; /* Hidden by default */
  }

  .cog-wheel-1 {
    width: 36px;
    height: 36px;
    top: 5px;
    right: -30px;
    background-image: url('/images/splash-logo.png');
    animation: spin 8s linear infinite;
  }

  .cog-wheel-2 {
    width: 20px;
    height: 20px;
    bottom: -5px;
    right: -40px;
    background-image: url('/images/splash-logo.png');
    animation: spin-reverse 6s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes spin-reverse {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(-360deg); }
  }

  /* Ultra-wide screens (1920px+) - Enhanced desktop experience */
  @media (min-width: 1920px) {
    .splash-container {
      width: clamp(900px, 50vw, 1200px);
      height: clamp(520px, 40vh, 700px);
    }

    .splash-app-name {
      font-size: clamp(32px, 2.5vw, 42px);
    }

    .splash-library-info {
      font-size: clamp(18px, 1.5vw, 24px);
    }

    .splash-description {
      font-size: clamp(20px, 1.8vw, 28px);
    }

    .splash-legal {
      font-size: clamp(14px, 1.2vw, 18px);
    }
  }

  /* Large tablets and small laptops (769px - 1024px) */
  @media (max-width: 1024px) and (min-width: 769px) {
    .splash-container {
      width: clamp(700px, 90vw, 850px);
      height: clamp(450px, 75vh, 550px);
      flex-direction: row;
    }

    .splash-info {
      width: 50% !important;
      padding: clamp(25px, 3vw, 35px) !important;
    }

    .splash-logo-container {
      width: 50% !important;
    }

    .splash-app-name {
      font-size: clamp(24px, 3vw, 30px);
    }

    .splash-library-info {
      font-size: clamp(16px, 2.2vw, 20px);
    }

    .splash-description {
      font-size: clamp(18px, 2.5vw, 22px);
    }
  }

  /* Tablets (481px - 768px) - Vertical layout with proper proportions */
  @media (max-width: 768px) and (min-width: 481px) {
    .splash-container {
      width: clamp(90%, 95vw, 650px);
      height: clamp(500px, 85vh, 600px);
      flex-direction: column;
      margin: clamp(10px, 2vh, 20px) auto;
    }

    .splash-logo-container {
      position: relative !important;
      width: 100% !important;
      height: clamp(30%, 35vh, 40%) !important;
      top: auto !important;
      right: auto !important;
      order: 1 !important;
      overflow: hidden !important;
      border-radius: var(--splash-border-radius-mobile) var(--splash-border-radius-mobile) 0 0 !important;
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%) !important;
    }

    .splash-logo-container::before {
      display: none !important;
    }

    .splash-logo-wrapper {
      width: 100% !important;
      height: 100% !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      overflow: hidden !important;
      position: relative !important;
    }

    .splash-logo {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover !important;
      object-position: center !important;
      transform: scale(1.0) !important;
      transform-origin: center !important;
    }

    .splash-info {
      width: 100% !important;
      height: clamp(60%, 65vh, 70%) !important;
      padding: clamp(20px, 4vw, 35px) clamp(25px, 4vw, 35px) !important;
      justify-content: space-between !important;
      display: flex !important;
      flex-direction: column !important;
      order: 2 !important;
      background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%) !important;
      border-radius: 0 0 var(--splash-border-radius-mobile) var(--splash-border-radius-mobile) !important;
    }

    .splash-app-icon {
      width: clamp(40px, 6vw, 55px) !important;
      height: clamp(40px, 6vw, 55px) !important;
      margin-bottom: clamp(6px, 1.5vw, 12px) !important;
    }

    .splash-app-name {
      font-size: clamp(18px, 3.5vw, 26px) !important;
      margin-bottom: clamp(4px, 1vw, 8px) !important;
    }

    .splash-library-info {
      font-size: clamp(11px, 2.5vw, 16px) !important;
      margin-bottom: clamp(4px, 1vw, 8px) !important;
    }

    .splash-description {
      font-size: clamp(13px, 2.8vw, 18px) !important;
      margin-bottom: clamp(8px, 1.5vw, 12px) !important;
      padding-bottom: clamp(4px, 1vw, 8px) !important;
    }

    .splash-legal {
      font-size: clamp(9px, 2vw, 12px) !important;
      margin-bottom: clamp(8px, 1.5vw, 15px) !important;
      max-width: 100% !important;
      line-height: clamp(1.2, 1.3, 1.4) !important;
    }

    .splash-loading-text {
      font-size: clamp(8px, 1.8vw, 11px) !important;
      margin-bottom: clamp(3px, 0.8vw, 6px) !important;
    }

    .splash-loading-dots {
      margin: clamp(4px, 1vw, 8px) 0 clamp(6px, 1.2vw, 10px) 0 !important;
      height: clamp(30px, 6vw, 45px) !important;
    }

    .splash-branding {
      margin-top: auto !important;
      padding-top: clamp(4px, 1vw, 8px) !important;
      flex-shrink: 0 !important;
    }

    .splash-branding-text {
      font-size: clamp(9px, 1.8vw, 12px) !important;
    }
  }

  /* Mobile phones (321px - 480px) - Fluid responsive design */
  @media (max-width: 480px) and (min-width: 321px) {
    .splash-container {
      width: clamp(92%, 95vw, 100%);
      height: clamp(400px, 85vh, 500px);
      margin: clamp(5px, 2vh, 15px) auto;
      flex-direction: column;
      min-height: 400px;
    }

    .splash-logo-container {
      position: relative !important;
      width: 100% !important;
      height: clamp(22%, 25vh, 30%) !important;
      min-height: clamp(80px, 15vh, 120px) !important;
      top: auto !important;
      right: auto !important;
      order: 1 !important;
      overflow: hidden !important;
      border-radius: clamp(12px, 4vw, 20px) clamp(12px, 4vw, 20px) 0 0 !important;
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%) !important;
    }

    .splash-logo-container::before {
      display: none !important;
    }

    .splash-logo-wrapper {
      width: 100% !important;
      height: 100% !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      overflow: hidden !important;
    }

    .splash-logo {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover !important;
      object-position: center !important;
      transform: scale(1.0) !important;
    }

    .splash-info {
      width: 100% !important;
      height: clamp(70%, 75vh, 78%) !important;
      clip-path: none !important;
      padding: clamp(12px, 3vw, 20px) clamp(15px, 3.5vw, 25px) !important;
      justify-content: space-between !important;
      display: flex !important;
      flex-direction: column !important;
      order: 2 !important;
    }

    .splash-app-icon {
      width: clamp(28px, 7vw, 40px) !important;
      height: clamp(28px, 7vw, 40px) !important;
      margin-bottom: clamp(4px, 1.2vw, 8px) !important;
    }

    .splash-app-name {
      font-size: clamp(12px, 3.5vw, 18px) !important;
      margin-bottom: clamp(2px, 0.8vw, 5px) !important;
      letter-spacing: clamp(-0.3px, -0.1vw, -0.1px) !important;
    }

    .splash-library-info {
      font-size: clamp(9px, 2.5vw, 13px) !important;
      margin-bottom: clamp(2px, 0.8vw, 5px) !important;
    }

    .splash-description {
      font-size: clamp(10px, 2.8vw, 15px) !important;
      margin-bottom: clamp(5px, 1.2vw, 8px) !important;
      font-weight: 500 !important;
      padding-bottom: clamp(2px, 0.8vw, 5px) !important;
    }

    .splash-legal {
      font-size: clamp(7px, 1.8vw, 10px) !important;
      margin-bottom: clamp(6px, 1.5vw, 10px) !important;
      line-height: clamp(1.1, 1.2, 1.3) !important;
      flex-shrink: 0 !important;
    }

    .splash-loading-text {
      font-size: clamp(6px, 1.5vw, 9px) !important;
      margin-bottom: clamp(2px, 0.6vw, 4px) !important;
      opacity: 1 !important;
    }

    .splash-loading-dots {
      margin: clamp(3px, 0.8vw, 6px) 0 clamp(5px, 1.2vw, 8px) 0 !important;
      height: clamp(25px, 6vw, 35px) !important;
    }

    .splash-branding {
      margin-top: auto !important;
      padding-top: clamp(3px, 0.8vw, 6px) !important;
      flex-shrink: 0 !important;
    }

    .splash-branding-text {
      font-size: clamp(7px, 1.8vw, 10px) !important;
      opacity: 1 !important;
      color: #555555 !important;
      line-height: clamp(1.1, 1.2, 1.3) !important;
    }
  }

  /* Extra small mobile devices (240px - 320px) - Ultra fluid design */
  @media (max-width: 320px) {
    .splash-container {
      width: clamp(95%, 98vw, 100%);
      height: clamp(350px, 90vh, 420px);
      margin: clamp(2px, 1vh, 8px) auto;
      flex-direction: column;
      min-height: 350px;
    }

    .splash-logo-container {
      position: relative !important;
      width: 100% !important;
      height: clamp(18%, 20vh, 25%) !important;
      min-height: clamp(60px, 12vh, 85px) !important;
      top: auto !important;
      right: auto !important;
      order: 1 !important;
      overflow: hidden !important;
      border-radius: clamp(8px, 3vw, 16px) clamp(8px, 3vw, 16px) 0 0 !important;
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%) !important;
    }

    .splash-logo-container::before {
      display: none !important;
    }

    .splash-logo-wrapper {
      width: 100% !important;
      height: 100% !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      overflow: hidden !important;
    }

    .splash-logo {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover !important;
      object-position: center !important;
      transform: scale(1.0) !important;
    }

    .splash-info {
      width: 100% !important;
      height: clamp(75%, 80vh, 82%) !important;
      clip-path: none !important;
      padding: clamp(8px, 2.5vw, 15px) clamp(12px, 3vw, 18px) !important;
      justify-content: space-between !important;
      display: flex !important;
      flex-direction: column !important;
      order: 2 !important;
    }

    .splash-app-icon {
      width: clamp(22px, 6vw, 32px) !important;
      height: clamp(22px, 6vw, 32px) !important;
      margin-bottom: clamp(3px, 1vw, 6px) !important;
    }

    .splash-app-name {
      font-size: clamp(10px, 3vw, 14px) !important;
      margin-bottom: clamp(1px, 0.5vw, 3px) !important;
      letter-spacing: clamp(-0.2px, -0.05vw, -0.1px) !important;
    }

    .splash-library-info {
      font-size: clamp(8px, 2.2vw, 11px) !important;
      margin-bottom: clamp(1px, 0.5vw, 3px) !important;
    }

    .splash-description {
      font-size: clamp(9px, 2.5vw, 12px) !important;
      margin-bottom: clamp(3px, 1vw, 6px) !important;
      font-weight: 500 !important;
      padding-bottom: clamp(1px, 0.5vw, 3px) !important;
    }

    .splash-legal {
      font-size: clamp(6px, 1.6vw, 8px) !important;
      margin-bottom: clamp(4px, 1.2vw, 7px) !important;
      line-height: clamp(1.0, 1.1, 1.2) !important;
      flex-shrink: 0 !important;
    }

    .splash-loading-text {
      font-size: clamp(5px, 1.3vw, 7px) !important;
      margin-bottom: clamp(1px, 0.4vw, 3px) !important;
      opacity: 1 !important;
    }

    .splash-loading-dots {
      margin: clamp(2px, 0.6vw, 4px) 0 clamp(3px, 1vw, 5px) 0 !important;
      height: clamp(20px, 5vw, 28px) !important;
    }

    .splash-branding {
      margin-top: auto !important;
      padding-top: clamp(2px, 0.6vw, 4px) !important;
      flex-shrink: 0 !important;
    }

    .splash-branding-text {
      font-size: clamp(6px, 1.6vw, 8px) !important;
      opacity: 1 !important;
      color: #555555 !important;
      line-height: clamp(1.0, 1.1, 1.2) !important;
    }
  }

  /* Micro devices (below 240px) - Emergency fallback */
  @media (max-width: 239px) {
    .splash-container {
      width: 100%;
      height: 100vh;
      margin: 0;
      border-radius: 0;
      flex-direction: column;
    }

    .splash-logo-container {
      height: 15% !important;
      min-height: 40px !important;
      border-radius: 0 !important;
    }

    .splash-info {
      height: 85% !important;
      padding: 5px 8px !important;
    }

    .splash-app-name {
      font-size: 8px !important;
    }

    .splash-library-info,
    .splash-description,
    .splash-legal,
    .splash-loading-text,
    .splash-branding-text {
      font-size: 6px !important;
      margin-bottom: 1px !important;
    }

    .splash-app-icon {
      width: 16px !important;
      height: 16px !important;
      margin-bottom: 2px !important;
    }

    .splash-loading-dots {
      height: 15px !important;
      margin: 1px 0 !important;
    }
  }

  /* Orientation-based responsive design */
  @media (orientation: landscape) and (max-height: 500px) {
    .splash-container {
      flex-direction: row !important;
      height: clamp(300px, 90vh, 400px) !important;
      width: clamp(600px, 95vw, 800px) !important;
    }

    .splash-logo-container {
      width: 40% !important;
      height: 100% !important;
      order: 2 !important;
      border-radius: 0 var(--splash-border-radius) var(--splash-border-radius) 0 !important;
    }

    .splash-info {
      width: 60% !important;
      height: 100% !important;
      order: 1 !important;
      padding: clamp(10px, 2vh, 20px) clamp(15px, 2vw, 25px) !important;
    }

    .splash-app-name {
      font-size: clamp(12px, 2.5vh, 18px) !important;
    }

    .splash-library-info {
      font-size: clamp(10px, 2vh, 14px) !important;
    }

    .splash-description {
      font-size: clamp(11px, 2.2vh, 16px) !important;
    }

    .splash-legal {
      font-size: clamp(8px, 1.6vh, 11px) !important;
    }

    .splash-loading-text {
      font-size: clamp(7px, 1.4vh, 10px) !important;
    }

    .splash-branding-text {
      font-size: clamp(7px, 1.4vh, 10px) !important;
    }
  }

  /* High DPI / Retina display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .splash-logo {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }

    .splash-app-icon-image {
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }

  /* Reduced motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .splash-container,
    .splash-logo,
    .splash-loading-dots,
    .splash-app-icon {
      animation: none !important;
      transition: none !important;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .splash-container {
      border: 2px solid;
      background: Canvas;
      color: CanvasText;
    }

    .splash-logo-container {
      background: ButtonFace;
      border: 1px solid ButtonText;
    }

    .splash-app-name,
    .splash-library-info,
    .splash-description,
    .splash-legal,
    .splash-loading-text,
    .splash-branding-text {
      color: CanvasText !important;
    }
  }

  /* Print styles */
  @media print {
    .splash-screen {
      display: none !important;
    }
  }

  /* Container queries for future-proofing */
  @supports (container-type: inline-size) {
    .splash-container {
      container-type: inline-size;
    }

    @container (max-width: 480px) {
      .splash-info {
        padding: clamp(10px, 3cqw, 20px) !important;
      }

      .splash-app-name {
        font-size: clamp(12px, 4cqw, 18px) !important;
      }

      .splash-description {
        font-size: clamp(10px, 3cqw, 14px) !important;
      }
    }
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Custom colors for 3D header */
  .bg-gray-850 {
    background-color: #1f2937;
  }

  /* General responsive design for non-splash elements */
  @media (max-width: 768px) {
    .app-dock {
      @apply w-[90%] max-w-sm;
    }

    .filter-panel-container {
      @apply w-full;
    }

    /* Mobile book grid handled by main responsive styles */

    /* VSSKit Search responsive styles */
    .vsskit-search-input {
      width: 100%;
      max-width: none;
    }

    .vsskit-filter-btn {
      height: 40px;
      width: 40px;
    }
  }

  @media (max-width: 640px) {
    .vsskit-search-input {
      width: 280px;
      height: 42px;
    }

    .vsskit-search-field {
      @apply px-10 py-2 text-sm;
    }

    .vsskit-filter-btn {
      height: 42px;
      width: 42px;
    }
  }
}
