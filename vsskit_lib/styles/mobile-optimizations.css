/* Mobile and Touch Optimizations for ZiWorkspace */

/* Base touch optimizations */
.touch-device {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* Touch target sizes */
.touch-device button,
.touch-device .clickable,
.touch-device .interactive {
  min-height: var(--touch-target-size, 44px);
  min-width: var(--touch-target-size, 44px);
}

/* Mobile workspace optimizations */
.mobile-workspace {
  font-size: 16px; /* Prevent zoom on iOS */
}

.mobile-workspace .desktop-icon {
  transform: scale(1.2);
  margin: 8px;
}

.mobile-workspace .taskbar {
  height: 60px;
  padding: 8px 12px;
}

.mobile-workspace .taskbar button {
  min-height: 48px;
  min-width: 48px;
  font-size: 18px;
}

/* Low-end device optimizations - More specific selectors to preserve original styling */
.low-end-device .mobile-optimization-target {
  animation-duration: calc(var(--animation-duration, 200ms) * 0.5) !important;
  transition-duration: calc(var(--animation-duration, 200ms) * 0.5) !important;
}

/* Only apply fallbacks to specific low-performance elements, not all liquid glass */
.low-end-device .liquid-glass.performance-fallback,
.low-end-device .glassmorphism.performance-fallback {
  backdrop-filter: none !important;
  background: rgba(0, 0, 0, 0.8) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.low-end-device .complex-gradient.performance-fallback {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6)) !important;
}

/* Disable expensive effects on low-end devices */
.low-end-device .disable-on-low-end {
  display: none !important;
}

.low-end-device .blur-effect {
  filter: none !important;
  backdrop-filter: none !important;
}

/* Mobile-specific layouts */
@media (max-width: 768px) {
  .mobile-workspace .desktop-widgets {
    display: none;
  }
  
  .mobile-workspace .app-dock {
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    flex-direction: row;
    max-width: 90vw;
    overflow-x: auto;
    overflow-y: hidden;
  }
  
  .mobile-workspace .ziassist-sidebar {
    width: 100vw !important;
    height: 100vh !important;
    top: 0 !important;
    left: 0 !important;
  }
}

/* Small screen optimizations */
@media (max-width: 480px) {
  .mobile-workspace {
    font-size: 14px;
  }
  
  .mobile-workspace .desktop-icon {
    transform: scale(1.0);
    margin: 4px;
  }
  
  .mobile-workspace .taskbar {
    height: 50px;
    padding: 4px 8px;
  }
  
  .mobile-workspace .taskbar button {
    min-height: 42px;
    min-width: 42px;
    font-size: 16px;
  }
}

/* Touch gesture optimizations */
.touch-device .draggable {
  touch-action: none;
}

.touch-device .scrollable {
  touch-action: pan-y;
  -webkit-overflow-scrolling: touch;
}

.touch-device .horizontal-scroll {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
}

/* Performance optimizations */
.mobile-workspace .will-change-transform {
  will-change: transform;
}

.mobile-workspace .will-change-opacity {
  will-change: opacity;
}

/* Reduce motion for accessibility and performance */
@media (prefers-reduced-motion: reduce) {
  .mobile-workspace *,
  .mobile-workspace *::before,
  .mobile-workspace *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support - Only for accessibility elements */
@media (prefers-contrast: high) {
  .mobile-workspace .liquid-glass.accessibility-target,
  .mobile-workspace .glassmorphism.accessibility-target {
    background: rgba(0, 0, 0, 0.9) !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
  }
}

/* Dark mode optimizations for mobile */
@media (prefers-color-scheme: dark) {
  .mobile-workspace {
    background-color: #000;
  }
  
  .mobile-workspace .text-adaptive {
    color: #fff;
  }
}

/* Light mode optimizations for mobile */
@media (prefers-color-scheme: light) {
  .mobile-workspace {
    background-color: #fff;
  }
  
  .mobile-workspace .text-adaptive {
    color: #000;
  }
}

/* Tablet-specific optimizations */
.tablet-device .desktop-widgets {
  transform: scale(0.9);
}

.tablet-device .app-dock {
  max-width: 80vw;
}

/* Desktop optimizations when not mobile */
.desktop-device .mobile-only {
  display: none !important;
}

/* Focus styles for keyboard navigation */
.mobile-workspace button:focus,
.mobile-workspace .interactive:focus {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

/* Safe area support for notched devices */
.mobile-workspace {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Smooth scrolling for mobile */
.mobile-workspace .smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Loading states optimized for mobile */
.mobile-workspace .loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Optimize text rendering on mobile */
.mobile-workspace {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Memory optimization - limit concurrent animations */
.low-end-device .animated:nth-child(n+4) {
  animation: none !important;
}

/* Battery optimization - reduce frame rate */
.low-end-device .high-frequency-animation {
  animation-timing-function: steps(10) !important;
}
