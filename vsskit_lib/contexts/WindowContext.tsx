'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

interface WindowState {
  id: string
  title: string
  isOpen: boolean
  isMinimized: boolean
  isMaximized: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
}

interface WindowContextType {
  windows: WindowState[]
  hasOpenWindows: boolean
  addWindow: (window: Omit<WindowState, 'id'>) => string
  removeWindow: (id: string) => void
  updateWindow: (id: string, updates: Partial<WindowState>) => void
  minimizeWindow: (id: string) => void
  maximizeWindow: (id: string) => void
  restoreWindow: (id: string) => void
  closeWindow: (id: string) => void
  getWindow: (id: string) => WindowState | undefined
}

const WindowContext = createContext<WindowContextType | undefined>(undefined)

export function WindowProvider({ children }: { children: React.ReactNode }) {
  const [windows, setWindows] = useState<WindowState[]>([])

  // Calculate if there are any open windows (not minimized)
  const hasOpenWindows = windows.some(window => window.isOpen && !window.isMinimized)

  const addWindow = (windowData: Omit<WindowState, 'id'>): string => {
    const id = `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newWindow: WindowState = {
      ...windowData,
      id,
      isOpen: true
    }
    setWindows(prev => [...prev, newWindow])
    return id
  }

  const removeWindow = (id: string) => {
    setWindows(prev => prev.filter(window => window.id !== id))
  }

  const updateWindow = (id: string, updates: Partial<WindowState>) => {
    setWindows(prev => prev.map(window => 
      window.id === id ? { ...window, ...updates } : window
    ))
  }

  const minimizeWindow = (id: string) => {
    updateWindow(id, { isMinimized: true, isMaximized: false })
  }

  const maximizeWindow = (id: string) => {
    updateWindow(id, { isMaximized: true, isMinimized: false })
  }

  const restoreWindow = (id: string) => {
    updateWindow(id, { isMinimized: false, isMaximized: false })
  }

  const closeWindow = (id: string) => {
    updateWindow(id, { isOpen: false })
    // Remove after animation
    setTimeout(() => removeWindow(id), 300)
  }

  const getWindow = (id: string): WindowState | undefined => {
    return windows.find(window => window.id === id)
  }

  // Debug logging
  useEffect(() => {
    console.log('🪟 Window Context:', {
      totalWindows: windows.length,
      openWindows: windows.filter(w => w.isOpen).length,
      hasOpenWindows,
      windows: windows.map(w => ({ id: w.id, title: w.title, isOpen: w.isOpen, isMinimized: w.isMinimized }))
    })
  }, [windows, hasOpenWindows])

  const value: WindowContextType = {
    windows,
    hasOpenWindows,
    addWindow,
    removeWindow,
    updateWindow,
    minimizeWindow,
    maximizeWindow,
    restoreWindow,
    closeWindow,
    getWindow
  }

  return (
    <WindowContext.Provider value={value}>
      {children}
    </WindowContext.Provider>
  )
}

export function useWindows() {
  const context = useContext(WindowContext)
  if (context === undefined) {
    // Return safe defaults instead of throwing error
    console.warn('useWindows used outside WindowProvider, returning defaults')
    return {
      windows: [],
      hasOpenWindows: false,
      addWindow: () => '',
      removeWindow: () => {},
      updateWindow: () => {},
      minimizeWindow: () => {},
      maximizeWindow: () => {},
      restoreWindow: () => {},
      closeWindow: () => {},
      getWindow: () => undefined
    } as WindowContextType
  }
  return context
}

// Hook for components to easily check if they should collapse
export function useCollapseState() {
  const context = useContext(WindowContext)

  // Safe fallback if context is not available
  if (context === undefined) {
    console.warn('useCollapseState used outside WindowProvider, returning defaults')
    return {
      shouldCollapse: false,
      shouldExpand: true
    }
  }

  const { hasOpenWindows } = context

  return {
    shouldCollapse: hasOpenWindows,
    shouldExpand: !hasOpenWindows
  }
}
