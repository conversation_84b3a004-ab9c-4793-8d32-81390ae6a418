'use client'

export interface DeviceCapabilities {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouch: boolean
  isLowEnd: boolean
  screenSize: 'small' | 'medium' | 'large'
  performanceLevel: 'low' | 'medium' | 'high'
  supportsAdvancedEffects: boolean
  maxTouchPoints: number
  deviceMemory: number
  hardwareConcurrency: number
}

export interface OptimizationSettings {
  enableAnimations: boolean
  enableBlur: boolean
  enableShadows: boolean
  enableGradients: boolean
  enableTransitions: boolean
  maxBlurRadius: number
  animationDuration: number
  touchTargetSize: number
}

class MobileOptimizationManager {
  private capabilities: DeviceCapabilities | null = null
  private settings: OptimizationSettings | null = null

  detectDeviceCapabilities(): DeviceCapabilities {
    if (typeof window === 'undefined') {
      return this.getSSRFallback()
    }

    const userAgent = navigator.userAgent
    const isMobile = /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || 
                    window.innerWidth <= 768
    const isTablet = /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)|Android(?=.*\bTablet\b)/i.test(userAgent) ||
                    (window.innerWidth > 768 && window.innerWidth <= 1024)
    const isDesktop = !isMobile && !isTablet
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    
    // Enhanced low-end device detection
    const deviceMemory = (navigator as any).deviceMemory || 4
    const hardwareConcurrency = navigator.hardwareConcurrency || 4
    const isLowEnd = 
      deviceMemory < 4 ||
      hardwareConcurrency < 4 ||
      /Android.*Chrome\/[0-5]|iPhone.*OS [0-9]_|iPad.*OS [0-9]_/.test(userAgent) ||
      window.innerWidth < 480

    // Screen size classification
    let screenSize: 'small' | 'medium' | 'large' = 'medium'
    if (window.innerWidth < 480) screenSize = 'small'
    else if (window.innerWidth > 1200) screenSize = 'large'

    // Performance level assessment
    let performanceLevel: 'low' | 'medium' | 'high' = 'medium'
    if (isLowEnd || deviceMemory < 2) performanceLevel = 'low'
    else if (deviceMemory >= 8 && hardwareConcurrency >= 8) performanceLevel = 'high'

    const capabilities: DeviceCapabilities = {
      isMobile,
      isTablet,
      isDesktop,
      isTouch,
      isLowEnd,
      screenSize,
      performanceLevel,
      supportsAdvancedEffects: performanceLevel !== 'low' && !isLowEnd,
      maxTouchPoints: navigator.maxTouchPoints || 0,
      deviceMemory,
      hardwareConcurrency
    }

    this.capabilities = capabilities
    return capabilities
  }

  generateOptimizationSettings(capabilities?: DeviceCapabilities): OptimizationSettings {
    const caps = capabilities || this.capabilities || this.detectDeviceCapabilities()
    
    let settings: OptimizationSettings

    if (caps.performanceLevel === 'low' || caps.isLowEnd) {
      // Low-end device settings
      settings = {
        enableAnimations: false,
        enableBlur: false,
        enableShadows: false,
        enableGradients: false,
        enableTransitions: true,
        maxBlurRadius: 0,
        animationDuration: 150,
        touchTargetSize: caps.isTouch ? 48 : 32
      }
    } else if (caps.performanceLevel === 'medium') {
      // Medium performance settings
      settings = {
        enableAnimations: true,
        enableBlur: true,
        enableShadows: true,
        enableGradients: true,
        enableTransitions: true,
        maxBlurRadius: 8,
        animationDuration: 200,
        touchTargetSize: caps.isTouch ? 44 : 32
      }
    } else {
      // High performance settings
      settings = {
        enableAnimations: true,
        enableBlur: true,
        enableShadows: true,
        enableGradients: true,
        enableTransitions: true,
        maxBlurRadius: 20,
        animationDuration: 300,
        touchTargetSize: caps.isTouch ? 44 : 32
      }
    }

    this.settings = settings
    return settings
  }

  applyMobileOptimizations(capabilities?: DeviceCapabilities) {
    const caps = capabilities || this.capabilities || this.detectDeviceCapabilities()
    const settings = this.generateOptimizationSettings(caps)

    // Apply CSS classes for optimization
    const body = document.body
    
    // Remove existing optimization classes
    body.classList.remove('mobile-device', 'tablet-device', 'desktop-device', 'touch-device', 'low-end-device', 'high-performance')
    
    // Add device type classes
    if (caps.isMobile) body.classList.add('mobile-device')
    if (caps.isTablet) body.classList.add('tablet-device')
    if (caps.isDesktop) body.classList.add('desktop-device')
    if (caps.isTouch) body.classList.add('touch-device')
    if (caps.isLowEnd) body.classList.add('low-end-device')
    if (caps.performanceLevel === 'high') body.classList.add('high-performance')

    // Apply CSS custom properties for dynamic optimization
    const root = document.documentElement
    root.style.setProperty('--touch-target-size', `${settings.touchTargetSize}px`)
    root.style.setProperty('--max-blur-radius', `${settings.maxBlurRadius}px`)
    root.style.setProperty('--animation-duration', `${settings.animationDuration}ms`)
    root.style.setProperty('--enable-animations', settings.enableAnimations ? '1' : '0')
    root.style.setProperty('--enable-blur', settings.enableBlur ? '1' : '0')
    root.style.setProperty('--enable-shadows', settings.enableShadows ? '1' : '0')

    return { capabilities: caps, settings }
  }

  private getSSRFallback(): DeviceCapabilities {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      isTouch: false,
      isLowEnd: false,
      screenSize: 'large',
      performanceLevel: 'medium',
      supportsAdvancedEffects: true,
      maxTouchPoints: 0,
      deviceMemory: 4,
      hardwareConcurrency: 4
    }
  }

  getCapabilities(): DeviceCapabilities {
    return this.capabilities || this.detectDeviceCapabilities()
  }

  getSettings(): OptimizationSettings {
    return this.settings || this.generateOptimizationSettings()
  }

  // Touch event helpers
  getTouchEventOptions() {
    return {
      passive: true,
      capture: false
    }
  }

  // Debounced resize handler for performance
  createResizeHandler(callback: () => void, delay: number = 250) {
    let timeoutId: NodeJS.Timeout
    return () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(callback, delay)
    }
  }

  // Intersection observer for performance
  createIntersectionObserver(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
    const defaultOptions = {
      rootMargin: '50px',
      threshold: 0.1
    }
    return new IntersectionObserver(callback, { ...defaultOptions, ...options })
  }
}

// Singleton instance
export const mobileOptimization = new MobileOptimizationManager()

// React hook for mobile optimization
export function useMobileOptimization() {
  const [capabilities, setCapabilities] = useState<DeviceCapabilities | null>(null)
  const [settings, setSettings] = useState<OptimizationSettings | null>(null)

  useEffect(() => {
    const result = mobileOptimization.applyMobileOptimizations()
    setCapabilities(result.capabilities)
    setSettings(result.settings)

    const handleResize = mobileOptimization.createResizeHandler(() => {
      const newResult = mobileOptimization.applyMobileOptimizations()
      setCapabilities(newResult.capabilities)
      setSettings(newResult.settings)
    })

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return { capabilities, settings }
}

// Import React hooks
import { useState, useEffect } from 'react'
