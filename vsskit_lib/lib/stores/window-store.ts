import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

interface WindowState {
  id: string
  title: string
  isOpen: boolean
  isMinimized: boolean
  isMaximized: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
}

interface WindowStore {
  windows: WindowState[]
  hasOpenWindows: boolean
  
  // Actions
  addWindow: (window: Omit<WindowState, 'id'>) => string
  removeWindow: (id: string) => void
  updateWindow: (id: string, updates: Partial<WindowState>) => void
  minimizeWindow: (id: string) => void
  maximizeWindow: (id: string) => void
  restoreWindow: (id: string) => void
  closeWindow: (id: string) => void
  getWindow: (id: string) => WindowState | undefined
  
  // Computed
  updateHasOpenWindows: () => void
}

export const useWindowStore = create<WindowStore>()(
  subscribeWithSelector((set, get) => ({
    windows: [],
    hasOpenWindows: false,

    addWindow: (windowData) => {
      const id = `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const newWindow: WindowState = {
        ...windowData,
        id,
        isOpen: true
      }
      
      set((state) => ({
        windows: [...state.windows, newWindow]
      }))
      
      get().updateHasOpenWindows()
      return id
    },

    removeWindow: (id) => {
      set((state) => ({
        windows: state.windows.filter(window => window.id !== id)
      }))
      get().updateHasOpenWindows()
    },

    updateWindow: (id, updates) => {
      set((state) => ({
        windows: state.windows.map(window => 
          window.id === id ? { ...window, ...updates } : window
        )
      }))
      get().updateHasOpenWindows()
    },

    minimizeWindow: (id) => {
      get().updateWindow(id, { isMinimized: true, isMaximized: false })
    },

    maximizeWindow: (id) => {
      get().updateWindow(id, { isMaximized: true, isMinimized: false })
    },

    restoreWindow: (id) => {
      get().updateWindow(id, { isMinimized: false, isMaximized: false })
    },

    closeWindow: (id) => {
      get().updateWindow(id, { isOpen: false })
      // Remove after animation
      setTimeout(() => get().removeWindow(id), 300)
    },

    getWindow: (id) => {
      return get().windows.find(window => window.id === id)
    },

    updateHasOpenWindows: () => {
      const windows = get().windows
      const hasOpen = windows.some(window => window.isOpen && !window.isMinimized)
      set({ hasOpenWindows: hasOpen })
    }
  }))
)

// Hook for collapse state
export function useCollapseState() {
  const hasOpenWindows = useWindowStore(state => state.hasOpenWindows)
  
  return {
    shouldCollapse: hasOpenWindows,
    shouldExpand: !hasOpenWindows
  }
}

// Subscribe to changes for debugging
if (typeof window !== 'undefined') {
  useWindowStore.subscribe(
    (state) => state.windows,
    (windows) => {
      console.log('🪟 Window Store Updated:', {
        totalWindows: windows.length,
        openWindows: windows.filter(w => w.isOpen).length,
        hasOpenWindows: useWindowStore.getState().hasOpenWindows,
        windows: windows.map(w => ({ 
          id: w.id, 
          title: w.title, 
          isOpen: w.isOpen, 
          isMinimized: w.isMinimized 
        }))
      })
    }
  )
}
