import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

// Import app store to sync with existing window management
let appStore: any = null
if (typeof window !== 'undefined') {
  import('@/lib/stores/app-store').then(module => {
    appStore = module.useAppStore
    // Sync existing windows when store loads
    syncWithAppStore()
  })
}

interface WindowState {
  id: string
  title: string
  isOpen: boolean
  isMinimized: boolean
  isMaximized: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
}

interface WindowStore {
  windows: WindowState[]
  hasOpenWindows: boolean
  
  // Actions
  addWindow: (window: Omit<WindowState, 'id'>) => string
  removeWindow: (id: string) => void
  updateWindow: (id: string, updates: Partial<WindowState>) => void
  minimizeWindow: (id: string) => void
  maximizeWindow: (id: string) => void
  restoreWindow: (id: string) => void
  closeWindow: (id: string) => void
  getWindow: (id: string) => WindowState | undefined
  
  // Computed
  updateHasOpenWindows: () => void
}

export const useWindowStore = create<WindowStore>()(
  subscribeWithSelector((set, get) => ({
    windows: [],
    hasOpenWindows: false,

    addWindow: (windowData) => {
      const id = `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const newWindow: WindowState = {
        ...windowData,
        id,
        isOpen: true
      }
      
      set((state) => ({
        windows: [...state.windows, newWindow]
      }))
      
      get().updateHasOpenWindows()
      return id
    },

    removeWindow: (id) => {
      set((state) => ({
        windows: state.windows.filter(window => window.id !== id)
      }))
      get().updateHasOpenWindows()
    },

    updateWindow: (id, updates) => {
      set((state) => ({
        windows: state.windows.map(window => 
          window.id === id ? { ...window, ...updates } : window
        )
      }))
      get().updateHasOpenWindows()
    },

    minimizeWindow: (id) => {
      get().updateWindow(id, { isMinimized: true, isMaximized: false })
    },

    maximizeWindow: (id) => {
      get().updateWindow(id, { isMaximized: true, isMinimized: false })
    },

    restoreWindow: (id) => {
      get().updateWindow(id, { isMinimized: false, isMaximized: false })
    },

    closeWindow: (id) => {
      get().updateWindow(id, { isOpen: false })
      // Remove after animation
      setTimeout(() => get().removeWindow(id), 300)
    },

    getWindow: (id) => {
      return get().windows.find(window => window.id === id)
    },

    updateHasOpenWindows: () => {
      const windows = get().windows
      const hasOpen = windows.some(window => window.isOpen && !window.isMinimized)

      // Only update if there's a real change to prevent flashing
      const currentState = get().hasOpenWindows
      if (currentState !== hasOpen) {
        set({ hasOpenWindows: hasOpen })
        console.log('🔄 hasOpenWindows changed:', { from: currentState, to: hasOpen, windowCount: windows.length })
      }
    }
  }))
)

// Hook for collapse state
export function useCollapseState() {
  const hasOpenWindows = useWindowStore(state => state.hasOpenWindows)
  
  return {
    shouldCollapse: hasOpenWindows,
    shouldExpand: !hasOpenWindows
  }
}

// Sync function to bridge with app store
function syncWithAppStore() {
  if (!appStore) return

  try {
    const appStoreState = appStore.getState()
    const windowStore = useWindowStore.getState()

    // Get current windows from app store
    const appWindows = appStoreState.windows || []

    // Convert app store windows to window store format
    const syncedWindows = appWindows.map((appWindow: any) => ({
      id: appWindow.id,
      title: appWindow.title || 'Unknown Window',
      isOpen: true,
      isMinimized: appWindow.isMinimized || false,
      isMaximized: appWindow.isMaximized || false,
      position: appWindow.position || { x: 100, y: 100 },
      size: appWindow.size || { width: 800, height: 600 }
    }))

    // Only update if there's a real change
    const currentWindowIds = windowStore.windows.map(w => w.id).sort()
    const newWindowIds = syncedWindows.map(w => w.id).sort()

    if (JSON.stringify(currentWindowIds) !== JSON.stringify(newWindowIds)) {
      // Preserve ZiAssist windows (they start with 'ziassist-')
      const ziAssistWindows = windowStore.windows.filter(w => w.id.startsWith('ziassist-'))

      // Update window store with app windows + ZiAssist windows
      useWindowStore.setState({
        windows: [...syncedWindows, ...ziAssistWindows]
      })

      // Update hasOpenWindows
      windowStore.updateHasOpenWindows()

      console.log('🔄 Synced app store windows to window store:', {
        appWindows: syncedWindows.length,
        ziAssistWindows: ziAssistWindows.length,
        total: syncedWindows.length + ziAssistWindows.length
      })
    }
  } catch (error) {
    console.warn('Failed to sync with app store:', error)
  }
}

// Subscribe to app store changes to keep in sync
if (typeof window !== 'undefined') {
  // Balanced sync frequency - not too fast to cause flashing, not too slow to miss changes
  setInterval(() => {
    if (appStore) {
      syncWithAppStore()
    }
  }, 1000) // Check every 1 second for stable performance
}

// Subscribe to changes for debugging
if (typeof window !== 'undefined') {
  useWindowStore.subscribe(
    (state) => state.windows,
    (windows) => {
      console.log('🪟 Window Store Updated:', {
        totalWindows: windows.length,
        openWindows: windows.filter(w => w.isOpen).length,
        hasOpenWindows: useWindowStore.getState().hasOpenWindows,
        windows: windows.map(w => ({
          id: w.id,
          title: w.title,
          isOpen: w.isOpen,
          isMinimized: w.isMinimized
        }))
      })
    }
  )
}
