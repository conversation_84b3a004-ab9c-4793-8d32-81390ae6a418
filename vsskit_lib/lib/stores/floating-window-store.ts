'use client'

import { create } from 'zustand'

export interface FloatingWindowState {
  id: string
  title: string
  isOpen: boolean
  isMinimized: boolean
  position: { x: number; y: number }
  size: { width: number; height: number }
  zIndex: number
  content: 'ziexplorer' | 'ziwidgets' | 'custom'
  windowType?: 'panel' | 'app'
}

interface FloatingWindowStore {
  windows: FloatingWindowState[]
  nextZIndex: number
  
  // Actions
  openWindow: (windowConfig: Partial<FloatingWindowState> & { id: string; title: string; content: FloatingWindowState['content']; windowType?: 'panel' | 'app' }) => void
  closeWindow: (id: string) => void
  minimizeWindow: (id: string) => void
  restoreWindow: (id: string) => void
  updateWindowPosition: (id: string, position: { x: number; y: number }) => void
  updateWindowSize: (id: string, size: { width: number; height: number }) => void
  bringToFront: (id: string) => void
  closeAllWindows: () => void
  
  // Getters
  getWindow: (id: string) => FloatingWindowState | undefined
  isWindowOpen: (id: string) => boolean
  hasOpenWindows: () => boolean
}

export const useFloatingWindowStore = create<FloatingWindowStore>((set, get) => ({
  windows: [],
  nextZIndex: 1000,

  openWindow: (windowConfig) => {
    const existingWindow = get().windows.find(w => w.id === windowConfig.id)
    
    if (existingWindow) {
      // If window exists, just restore and bring to front
      set(state => ({
        windows: state.windows.map(w => 
          w.id === windowConfig.id 
            ? { ...w, isOpen: true, isMinimized: false, zIndex: state.nextZIndex }
            : w
        ),
        nextZIndex: state.nextZIndex + 1
      }))
    } else {
      // Create new window
      const newWindow: FloatingWindowState = {
        id: windowConfig.id,
        title: windowConfig.title,
        content: windowConfig.content,
        isOpen: true,
        isMinimized: false,
        position: windowConfig.position || { x: 100 + (get().windows.length * 30), y: 100 + (get().windows.length * 30) },
        size: windowConfig.size || { width: 500, height: 400 },
        zIndex: get().nextZIndex,
        windowType: windowConfig.windowType || 'app',
        ...windowConfig
      }
      
      set(state => ({
        windows: [...state.windows, newWindow],
        nextZIndex: state.nextZIndex + 1
      }))
    }
  },

  closeWindow: (id) => {
    set(state => ({
      windows: state.windows.map(w => 
        w.id === id ? { ...w, isOpen: false } : w
      )
    }))
  },

  minimizeWindow: (id) => {
    set(state => ({
      windows: state.windows.map(w => 
        w.id === id ? { ...w, isMinimized: true } : w
      )
    }))
  },

  restoreWindow: (id) => {
    set(state => ({
      windows: state.windows.map(w => 
        w.id === id ? { ...w, isMinimized: false, zIndex: state.nextZIndex } : w
      ),
      nextZIndex: state.nextZIndex + 1
    }))
  },

  updateWindowPosition: (id, position) => {
    set(state => ({
      windows: state.windows.map(w => 
        w.id === id ? { ...w, position } : w
      )
    }))
  },

  updateWindowSize: (id, size) => {
    set(state => ({
      windows: state.windows.map(w => 
        w.id === id ? { ...w, size } : w
      )
    }))
  },

  bringToFront: (id) => {
    set(state => ({
      windows: state.windows.map(w => 
        w.id === id ? { ...w, zIndex: state.nextZIndex } : w
      ),
      nextZIndex: state.nextZIndex + 1
    }))
  },

  closeAllWindows: () => {
    set(state => ({
      windows: state.windows.map(w => ({ ...w, isOpen: false }))
    }))
  },

  getWindow: (id) => {
    return get().windows.find(w => w.id === id)
  },

  isWindowOpen: (id) => {
    const window = get().windows.find(w => w.id === id)
    return window ? window.isOpen && !window.isMinimized : false
  },

  hasOpenWindows: () => {
    return get().windows.some(w => w.isOpen && !w.isMinimized)
  }
}))

// Hook for checking if floating windows should trigger collapse
export function useFloatingWindowCollapse() {
  const hasOpenWindows = useFloatingWindowStore(state => state.hasOpenWindows())
  
  return {
    shouldCollapse: hasOpenWindows,
    shouldExpand: !hasOpenWindows
  }
}
