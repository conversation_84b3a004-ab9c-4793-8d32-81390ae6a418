'use client'

import { useEffect, useRef, useState } from 'react'

export interface TouchGestureOptions {
  onTap?: (event: TouchEvent) => void
  onDoubleTap?: (event: TouchEvent) => void
  onLongPress?: (event: TouchEvent) => void
  onSwipe?: (direction: 'up' | 'down' | 'left' | 'right', event: TouchEvent) => void
  onPinch?: (scale: number, event: TouchEvent) => void
  longPressDelay?: number
  swipeThreshold?: number
  doubleTapDelay?: number
}

export function useTouchGestures(options: TouchGestureOptions) {
  const elementRef = useRef<HTMLElement>(null)
  const [touchState, setTouchState] = useState({
    startX: 0,
    startY: 0,
    startTime: 0,
    lastTapTime: 0,
    longPressTimer: null as NodeJS.Timeout | null,
    initialDistance: 0,
    isLongPress: false
  })

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const {
      onTap,
      onDoubleTap,
      onLongPress,
      onSwipe,
      onPinch,
      longPressDelay = 500,
      swipeThreshold = 50,
      doubleTapDelay = 300
    } = options

    // Calculate distance between two touches
    const getDistance = (touches: TouchList) => {
      if (touches.length < 2) return 0
      const touch1 = touches[0]
      const touch2 = touches[1]
      return Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
    }

    const handleTouchStart = (event: TouchEvent) => {
      const touch = event.touches[0]
      const now = Date.now()

      setTouchState(prev => ({
        ...prev,
        startX: touch.clientX,
        startY: touch.clientY,
        startTime: now,
        isLongPress: false,
        initialDistance: getDistance(event.touches)
      }))

      // Start long press timer
      if (onLongPress) {
        const timer = setTimeout(() => {
          setTouchState(prev => ({ ...prev, isLongPress: true }))
          onLongPress(event)
        }, longPressDelay)

        setTouchState(prev => ({ ...prev, longPressTimer: timer }))
      }
    }

    const handleTouchMove = (event: TouchEvent) => {
      // Handle pinch gesture
      if (onPinch && event.touches.length === 2) {
        const currentDistance = getDistance(event.touches)
        if (touchState.initialDistance > 0) {
          const scale = currentDistance / touchState.initialDistance
          onPinch(scale, event)
        }
      }

      // Cancel long press if finger moves too much
      const touch = event.touches[0]
      const deltaX = Math.abs(touch.clientX - touchState.startX)
      const deltaY = Math.abs(touch.clientY - touchState.startY)

      if ((deltaX > 10 || deltaY > 10) && touchState.longPressTimer) {
        clearTimeout(touchState.longPressTimer)
        setTouchState(prev => ({ ...prev, longPressTimer: null }))
      }
    }

    const handleTouchEnd = (event: TouchEvent) => {
      const touch = event.changedTouches[0]
      const now = Date.now()
      const deltaX = touch.clientX - touchState.startX
      const deltaY = touch.clientY - touchState.startY
      const deltaTime = now - touchState.startTime

      // Clear long press timer
      if (touchState.longPressTimer) {
        clearTimeout(touchState.longPressTimer)
        setTouchState(prev => ({ ...prev, longPressTimer: null }))
      }

      // Don't process other gestures if it was a long press
      if (touchState.isLongPress) {
        return
      }

      // Handle swipe gestures
      if (onSwipe && (Math.abs(deltaX) > swipeThreshold || Math.abs(deltaY) > swipeThreshold)) {
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          // Horizontal swipe
          onSwipe(deltaX > 0 ? 'right' : 'left', event)
        } else {
          // Vertical swipe
          onSwipe(deltaY > 0 ? 'down' : 'up', event)
        }
        return
      }

      // Handle tap gestures (only if not a swipe)
      if (Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10 && deltaTime < 300) {
        const timeSinceLastTap = now - touchState.lastTapTime

        if (onDoubleTap && timeSinceLastTap < doubleTapDelay) {
          // Double tap
          onDoubleTap(event)
          setTouchState(prev => ({ ...prev, lastTapTime: 0 }))
        } else {
          // Single tap (with delay to check for double tap)
          setTouchState(prev => ({ ...prev, lastTapTime: now }))
          
          if (onTap) {
            setTimeout(() => {
              // Only fire single tap if no double tap occurred
              if (touchState.lastTapTime === now) {
                onTap(event)
              }
            }, doubleTapDelay)
          }
        }
      }
    }

    // Add event listeners with passive option for better performance
    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: false })

    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
      
      if (touchState.longPressTimer) {
        clearTimeout(touchState.longPressTimer)
      }
    }
  }, [options, touchState.startX, touchState.startY, touchState.startTime, touchState.lastTapTime, touchState.longPressTimer, touchState.initialDistance, touchState.isLongPress])

  return elementRef
}

// Hook for handling mobile-specific interactions
export function useMobileInteractions() {
  const [isTouchDevice, setIsTouchDevice] = useState(false)

  useEffect(() => {
    setIsTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0)
  }, [])

  const preventZoom = (event: TouchEvent) => {
    if (event.touches.length > 1) {
      event.preventDefault()
    }
  }

  const enableTouchOptimizations = (element: HTMLElement) => {
    // Prevent zoom on double tap
    element.addEventListener('touchstart', preventZoom, { passive: false })
    
    // Improve touch responsiveness
    element.style.touchAction = 'manipulation'
    element.style.userSelect = 'none'
    element.style.webkitUserSelect = 'none'
    element.style.webkitTouchCallout = 'none'
    element.style.webkitTapHighlightColor = 'transparent'

    return () => {
      element.removeEventListener('touchstart', preventZoom)
    }
  }

  return {
    isTouchDevice,
    enableTouchOptimizations,
    preventZoom
  }
}

// Utility for creating touch-friendly button interactions
export function useTouchButton(onPress?: () => void, onLongPress?: () => void) {
  const buttonRef = useTouchGestures({
    onTap: onPress ? () => onPress() : undefined,
    onLongPress: onLongPress ? () => onLongPress() : undefined,
    longPressDelay: 600
  })

  const { enableTouchOptimizations } = useMobileInteractions()

  useEffect(() => {
    const element = buttonRef.current
    if (element) {
      return enableTouchOptimizations(element)
    }
  }, [buttonRef, enableTouchOptimizations])

  return buttonRef
}

// Hook for swipe navigation
export function useSwipeNavigation(
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  onSwipeUp?: () => void,
  onSwipeDown?: () => void
) {
  return useTouchGestures({
    onSwipe: (direction) => {
      switch (direction) {
        case 'left':
          onSwipeLeft?.()
          break
        case 'right':
          onSwipeRight?.()
          break
        case 'up':
          onSwipeUp?.()
          break
        case 'down':
          onSwipeDown?.()
          break
      }
    },
    swipeThreshold: 75 // Slightly higher threshold for navigation
  })
}
