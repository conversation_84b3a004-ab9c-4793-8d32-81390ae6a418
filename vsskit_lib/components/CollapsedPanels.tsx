'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCollapseState } from '@/lib/stores/window-store'
import Image from 'next/image'

// Collapsed Quick Access Panel (styled like mid panel)
export function CollapsedQuickAccess({ onAppLaunch }: { onAppLaunch: (appId: string) => void }) {
  const { shouldCollapse } = useCollapseState()
  const [isExpanded, setIsExpanded] = useState(false)

  const defaultApps = [
    { id: 'pdf-viewer', name: 'PDF Viewer', icon: '/images/icons/pdf.png' },
    { id: 'music-player', name: 'Music Player', icon: '/images/icons/music.png' },
    { id: 'image-viewer', name: 'Image Viewer', icon: '/images/icons/image.png' },
    { id: 'file-manager', name: 'File Manager', icon: '/images/icons/folder.png' }
  ]

  if (!shouldCollapse) return null

  return (
    <div className="relative">
      {/* Collapsed Panel Button */}
      <motion.button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 px-3 py-2 rounded-2xl transition-all duration-300"
        style={{
          background: `
            linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
            linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(139, 92, 246, 0.06) 100%)
          `,
          border: '1px solid rgba(255,255,255,0.15)',
          boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.2), 0 4px 8px rgba(0,0,0,0.1)'
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <div className="w-6 h-6 grid grid-cols-2 gap-0.5">
          {defaultApps.slice(0, 4).map((app, index) => (
            <div
              key={app.id}
              className="w-2.5 h-2.5 rounded-sm overflow-hidden"
              style={{
                background: `
                  radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%),
                  linear-gradient(135deg, rgba(147, 51, 234, 0.4) 0%, rgba(59, 130, 246, 0.3) 100%)
                `
              }}
            />
          ))}
        </div>
        <span className="text-white text-xs font-medium">Apps</span>
        <motion.div
          animate={{ rotate: isExpanded ? 180 : 0 }}
          className="text-white/70 text-xs"
        >
          ▼
        </motion.div>
      </motion.button>

      {/* Expanded Panel */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.9 }}
            className="absolute bottom-full mb-2 left-0 p-3 rounded-2xl backdrop-blur-2xl"
            style={{
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.08) 100%)
              `,
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.3)',
              minWidth: '200px'
            }}
          >
            <div className="grid grid-cols-2 gap-2">
              {defaultApps.map((app) => (
                <motion.button
                  key={app.id}
                  onClick={() => {
                    onAppLaunch(app.id)
                    setIsExpanded(false)
                  }}
                  className="flex flex-col items-center p-2 rounded-xl transition-all duration-200"
                  style={{
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)'
                  }}
                  whileHover={{ scale: 1.05, background: 'rgba(255,255,255,0.15)' }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="w-8 h-8 mb-1">
                    <Image
                      src={app.icon}
                      alt={app.name}
                      width={32}
                      height={32}
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <span className="text-white text-xs">{app.name}</span>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Collapsed ZiWidgets Panel (styled like time/system icons)
export function CollapsedZiWidgets() {
  const { shouldCollapse } = useCollapseState()
  const [activeWidget, setActiveWidget] = useState<string | null>(null)
  const [currentTime, setCurrentTime] = useState(new Date())

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  if (!shouldCollapse) return null

  const widgets = [
    { id: 'time', name: 'Time', icon: '🕐' },
    { id: 'weather', name: 'Weather', icon: '⛅' },
    { id: 'notes', name: 'Notes', icon: '📝' },
    { id: 'wallpaper', name: 'Wallpaper', icon: '🎨' }
  ]

  return (
    <div className="relative">
      {/* Collapsed Widget Button */}
      <motion.button
        onClick={() => setActiveWidget(activeWidget ? null : 'time')}
        className="flex items-center gap-2 px-2 py-1 rounded-xl transition-all duration-300"
        style={{
          background: 'rgba(255,255,255,0.08)',
          border: '1px solid rgba(255,255,255,0.12)',
          boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.15)'
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <span className="text-lg">🔧</span>
        <span className="text-white text-xs font-medium">Widgets</span>
      </motion.button>

      {/* Active Widget Display */}
      <AnimatePresence>
        {activeWidget && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.9 }}
            className="absolute bottom-full mb-2 right-0 p-4 rounded-2xl backdrop-blur-2xl"
            style={{
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.08) 100%)
              `,
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.3)',
              minWidth: '200px'
            }}
          >
            {/* Widget Selector */}
            <div className="flex gap-1 mb-3">
              {widgets.map((widget) => (
                <motion.button
                  key={widget.id}
                  onClick={() => setActiveWidget(widget.id)}
                  className={`px-2 py-1 rounded-lg text-xs transition-all ${
                    activeWidget === widget.id 
                      ? 'bg-white/20 text-white' 
                      : 'bg-white/10 text-white/70 hover:bg-white/15'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {widget.icon} {widget.name}
                </motion.button>
              ))}
            </div>

            {/* Widget Content */}
            <div className="text-white">
              {activeWidget === 'time' && (
                <div className="text-center">
                  <div className="text-2xl font-bold mb-1">
                    {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                  <div className="text-sm text-white/80">
                    {currentTime.toLocaleDateString([], { weekday: 'long', month: 'short', day: 'numeric' })}
                  </div>
                </div>
              )}
              {activeWidget === 'weather' && (
                <div className="text-center">
                  <div className="text-xl mb-1">⛅</div>
                  <div className="text-lg font-semibold">22°C</div>
                  <div className="text-xs text-white/80">Partly Cloudy</div>
                </div>
              )}
              {activeWidget === 'notes' && (
                <div>
                  <textarea
                    placeholder="Quick note..."
                    className="w-full h-16 bg-black/20 border border-white/30 rounded-lg p-2 text-white placeholder-white/60 text-xs resize-none focus:outline-none focus:ring-1 focus:ring-purple-400/50"
                  />
                </div>
              )}
              {activeWidget === 'wallpaper' && (
                <div className="text-center">
                  <div className="text-sm text-white/80 mb-2">Wallpaper Controls</div>
                  <button className="px-3 py-1 bg-purple-500/30 rounded-lg text-xs hover:bg-purple-500/40 transition-colors">
                    Change Wallpaper
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
