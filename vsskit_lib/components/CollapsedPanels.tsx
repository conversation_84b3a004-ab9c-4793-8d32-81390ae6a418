'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCollapseState } from '@/lib/stores/window-store'
import { useFloatingWindowStore } from '@/lib/stores/floating-window-store'
import { useWallpaperStore } from '@/lib/stores/wallpaper-store'
import Image from 'next/image'
import Tooltip from './Tooltip'

interface CollapsedQuickAccessProps {
  onAppLaunch?: (appName: string) => void
}

// ZiExplorer Panel - EXACTLY like middle section (same size, same style)
export function CollapsedQuickAccess({ onAppLaunch }: CollapsedQuickAccessProps) {
  const { shouldCollapse } = useCollapseState()
  const { openWindow } = useFloatingWindowStore()

  if (!shouldCollapse) return null

  const handleZiExplorerClick = () => {
    // Open in original desktop position (top-left where ZiExplorer panel was)
    // top-24 = 96px, left-12 = 48px, w-80 = 320px, h-96 = 384px
    openWindow({
      id: 'ziexplorer',
      title: 'ZiExplorer',
      content: 'ziexplorer',
      position: { x: 48, y: 96 },
      size: { width: 320, height: 384 },
      windowType: 'panel'
    })
  }

  const apps = [
    { id: 'zinote', name: 'ZiNote', icon: '/images/icons/Zinote.png' },
    { id: 'ziboard', name: 'ZiBoard', icon: '/images/icons/ziboard.png' },
    { id: 'ziimager', name: 'Ziimager', icon: '/images/icons/Ziimager.png' },
    { id: 'zicalculator', name: 'ZiCalculator', icon: '/images/icons/zicalculator.png' }
  ]

  return (
    <div className="relative">
      {/* Redesigned ZiExplorer - Sleek Docked Style */}
      <div className="flex items-center justify-center">
        <button
          onClick={handleZiExplorerClick}
          className="relative group transition-all duration-300 ease-out"
          style={{
            overflow: 'visible'
          }}
        >
          {/* Main Icon Container */}
          <div
            className="w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ease-out"
            style={{
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.12) 0%,
                  rgba(255, 255, 255, 0.06) 50%,
                  rgba(255, 255, 255, 0.08) 100%
                ),
                linear-gradient(135deg,
                  rgba(99, 102, 241, 0.25) 0%,
                  rgba(139, 92, 246, 0.22) 25%,
                  rgba(110, 140, 160, 0.20) 50%,
                  rgba(90, 150, 140, 0.18) 75%,
                  rgba(59, 130, 246, 0.15) 100%
                )
              `,
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.25),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1)
              `,
              backdropFilter: 'blur(12px) saturate(1.2)',
              transform: 'scale(1)',
              transformOrigin: 'center'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'scale(1.15) translateY(-2px)'
              e.currentTarget.style.boxShadow = `
                0 6px 20px rgba(0, 0, 0, 0.25),
                0 4px 12px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.35),
                inset 0 -1px 0 rgba(0, 0, 0, 0.15),
                0 0 20px rgba(99, 102, 241, 0.3)
              `
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1) translateY(0)'
              e.currentTarget.style.boxShadow = `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.25),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1)
              `
            }}
          >
            <div className="w-6 h-6 relative">
              <Image
                src="/images/icons/documents-folder.png"
                alt="ZiExplorer"
                width={24}
                height={24}
                className="object-contain filter drop-shadow-sm"
              />
            </div>
          </div>

          {/* Subtle Label */}
          <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <div className="px-2 py-1 bg-black/70 text-white text-xs rounded-md backdrop-blur-sm border border-white/20">
              ZiExplorer
            </div>
          </div>
        </button>
      </div>
    </div>
  )
}

// ZiWidgets Panel - Bigger, more visible with original widget arrangement
export function CollapsedZiWidgets() {
  const { shouldCollapse } = useCollapseState()
  const { openWindow } = useFloatingWindowStore()
  const { wallpapers, currentWallpaper, setWallpaper } = useWallpaperStore()
  const [currentTime, setCurrentTime] = useState(new Date())
  const [expandedWidget, setExpandedWidget] = useState<string | null>(null)
  const [showWallpaperGrid, setShowWallpaperGrid] = useState(false)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  if (!shouldCollapse) return null

  const handleZiWidgetsClick = () => {
    // Open in EXACT original desktop position from DesktopWidgets
    // Original position: { x: window.innerWidth - 420, y: 100 }
    // Original size: { width: 350, height: window.innerHeight - 280 }
    // Ensure it stays within desktop bounds and doesn't slide
    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight
    const panelWidth = 350
    const panelHeight = screenHeight - 280

    // Calculate exact position to align with desktop edge (not taskbar)
    const originalX = screenWidth - 420 // Exact original position
    const originalY = 100 // Exact original Y position

    // Ensure the window doesn't go outside desktop bounds
    const safeX = Math.max(20, Math.min(originalX, screenWidth - panelWidth - 20))
    const safeY = Math.max(20, originalY)
    const safeHeight = Math.min(panelHeight, screenHeight - 120) // Leave space for taskbar

    openWindow({
      id: 'ziwidgets',
      title: 'ZiWidgets',
      content: 'ziwidgets',
      position: { x: safeX, y: safeY },
      size: { width: panelWidth, height: safeHeight },
      windowType: 'panel'
    })
  }

  // Original widget arrangement from DesktopWidgets
  const widgets = [
    {
      id: 'time',
      label: 'Time',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10"/>
          <polyline points="12,6 12,12 16,14"/>
        </svg>
      )
    },
    {
      id: 'notes',
      label: 'Quick Notes',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
          <polyline points="14.5,2 14.5,8 20.5,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
        </svg>
      )
    },
    {
      id: 'wallpaper',
      label: 'Wallpapers',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="8.5" cy="8.5" r="1.5"/>
          <polyline points="21,15 16,10 5,21"/>
        </svg>
      )
    },
    {
      id: 'weather',
      label: 'Weather',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/>
        </svg>
      )
    }
  ]

  const handleWallpaperSelect = (wallpaperId: string) => {
    setWallpaper(wallpaperId)
    setShowWallpaperGrid(false)
  }

  return (
    <div className="flex items-center space-x-3 mr-4">
      {/* Single ZiWidgets Icon - Bigger and More Visible */}
      <div className="relative">
        <Tooltip content="ZiWidgets" placement="top">
          <button
            onClick={handleZiWidgetsClick}
            className="relative w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ease-out group"
            style={{
              background: `
                linear-gradient(135deg,
                  rgba(255, 255, 255, 0.18) 0%,
                  rgba(255, 255, 255, 0.10) 50%,
                  rgba(255, 255, 255, 0.15) 100%
                ),
                linear-gradient(135deg,
                  rgba(99, 102, 241, 0.25) 0%,
                  rgba(139, 92, 246, 0.22) 25%,
                  rgba(110, 140, 160, 0.20) 50%,
                  rgba(90, 150, 140, 0.18) 75%,
                  rgba(59, 130, 246, 0.15) 100%
                )
              `,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.35),
                inset 0 -1px 0 rgba(0, 0, 0, 0.12)
              `,
              backdropFilter: 'blur(12px) saturate(1.2)',
              color: 'rgba(255, 255, 255, 0.9)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'scale(1.15) translateY(-2px)'
              e.currentTarget.style.boxShadow = `
                0 6px 20px rgba(0, 0, 0, 0.25),
                0 4px 12px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.45),
                inset 0 -1px 0 rgba(0, 0, 0, 0.18),
                0 0 20px rgba(99, 102, 241, 0.35)
              `
              e.currentTarget.style.color = 'rgba(255, 255, 255, 1)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1) translateY(0)'
              e.currentTarget.style.boxShadow = `
                0 4px 12px rgba(0, 0, 0, 0.15),
                0 2px 6px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.35),
                inset 0 -1px 0 rgba(0, 0, 0, 0.12)
              `
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.9)'
            }}
          >
            {/* ZiWidgets Grid Icon */}
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="7" height="7"/>
              <rect x="14" y="3" width="7" height="7"/>
              <rect x="14" y="14" width="7" height="7"/>
              <rect x="3" y="14" width="7" height="7"/>
            </svg>
          </button>
        </Tooltip>

        {/* Time Display */}
        <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-white/75 font-medium">
          {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </div>
      </div>

    </div>
  )
}
