'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCollapseState } from '@/lib/stores/window-store'
import Image from 'next/image'

interface CollapsedQuickAccessProps {
  onAppLaunch?: (appName: string) => void
}

// Quick Access Panel - Styled like middle compartment (no dropdown, just icons)
export function CollapsedQuickAccess({ onAppLaunch }: CollapsedQuickAccessProps) {
  const { shouldCollapse } = useCollapseState()

  if (!shouldCollapse) return null

  const apps = [
    { id: 'zinote', name: '<PERSON>i<PERSON><PERSON>', icon: '/images/icons/Zinote.png' },
    { id: 'ziboard', name: '<PERSON>i<PERSON>oard', icon: '/images/icons/ziboard.png' },
    { id: 'ziimager', name: 'Ziimager', icon: '/images/icons/Ziimager.png' },
    { id: 'zicalculator', name: 'Zi<PERSON>alculator', icon: '/images/icons/zicalculator.png' }
  ]

  return (
    <div className="flex items-center">
      {/* Middle Compartment Style - Like PDF Viewer Section */}
      <div
        className="flex items-center px-3 py-2 rounded-2xl transition-all duration-300"
        style={{
          background: `
            linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
            linear-gradient(135deg, rgba(99, 102, 241, 0.18) 0%, rgba(139, 92, 246, 0.14) 50%, rgba(120, 120, 140, 0.12) 100%)
          `,
          border: '1px solid rgba(255,255,255,0.2)',
          borderTopColor: 'rgba(255,255,255,0.3)',
          borderLeftColor: 'rgba(255,255,255,0.25)',
          borderBottomColor: 'rgba(255,255,255,0.15)',
          borderRightColor: 'rgba(255,255,255,0.2)',
          boxShadow: `
            0 8px 32px rgba(0,0,0,0.2),
            0 4px 16px rgba(0,0,0,0.1),
            inset 0 1px 0 rgba(255,255,255,0.3),
            inset 0 -1px 0 rgba(0,0,0,0.1),
            inset 1px 0 0 rgba(255,255,255,0.2),
            inset -1px 0 0 rgba(0,0,0,0.05)
          `,
          backdropFilter: 'blur(20px) saturate(1.2) brightness(1.1)'
        }}
      >
        {/* App Icons - Like taskbar icons */}
        <div className="flex items-center space-x-2">
          {apps.map((app) => (
            <button
              key={app.id}
              onClick={() => onAppLaunch?.(app.id)}
              className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-110 hover:bg-white/10 group"
              title={app.name}
            >
              <div className="w-6 h-6 relative">
                <Image
                  src={app.icon}
                  alt={app.name}
                  width={24}
                  height={24}
                  className="object-contain opacity-90 group-hover:opacity-100 transition-opacity"
                />
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )

}

// ZiWidgets Panel - White SVG icons like traditional taskbar system icons (volume, wifi, etc.)
export function CollapsedZiWidgets() {
  const { shouldCollapse } = useCollapseState()
  const [currentTime, setCurrentTime] = useState(new Date())
  const [expandedWidget, setExpandedWidget] = useState<string | null>(null)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  if (!shouldCollapse) return null

  const widgets = [
    {
      id: 'notes',
      label: 'Quick Notes',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
          <polyline points="10,9 9,9 8,9"/>
        </svg>
      ),
      content: (
        <div className="p-4 w-64">
          <h4 className="text-white text-sm font-medium mb-3 drop-shadow-lg">Quick Notes</h4>
          <textarea
            placeholder="Jot down your thoughts..."
            className="w-full h-24 bg-black/20 border border-white/30 rounded-xl p-3 text-white placeholder-white/60 resize-none focus:outline-none focus:ring-2 focus:ring-purple-400/50 text-sm backdrop-blur-sm"
            style={{
              boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.2)'
            }}
          />
        </div>
      )
    },
    {
      id: 'weather',
      label: 'Weather',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/>
        </svg>
      ),
      content: (
        <div className="p-4 text-center min-w-[150px]">
          <div className="text-lg font-semibold text-white mb-1 drop-shadow-lg">22°C</div>
          <div className="text-xs text-white/80 drop-shadow-md">Partly Cloudy</div>
        </div>
      )
    },
    {
      id: 'wallpaper',
      label: 'Wallpaper',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="8.5" cy="8.5" r="1.5"/>
          <polyline points="21,15 16,10 5,21"/>
        </svg>
      ),
      content: (
        <div className="p-4 text-center min-w-[120px]">
          <div className="text-sm text-white font-medium mb-2 drop-shadow-lg">Wallpaper</div>
          <div className="text-xs text-white/80 drop-shadow-md">Quick Settings</div>
        </div>
      )
    },
    {
      id: 'time',
      label: 'Time & Date',
      icon: (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10"/>
          <polyline points="12,6 12,12 16,14"/>
        </svg>
      ),
      content: (
        <div className="p-4 text-center min-w-[200px]">
          <div className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
            {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
          <div className="text-sm text-white/80 drop-shadow-md">
            {currentTime.toLocaleDateString([], { weekday: 'long', month: 'short', day: 'numeric' })}
          </div>
        </div>
      )
    }
  ]

  return (
    <div className="flex items-center space-x-1">
      {widgets.map((widget) => (
        <div key={widget.id} className="relative">
          <button
            onClick={() => setExpandedWidget(expandedWidget === widget.id ? null : widget.id)}
            className="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-110 hover:bg-white/10 group text-white/80 hover:text-white"
            title={widget.label}
            style={{
              background: expandedWidget === widget.id ? 'rgba(255,255,255,0.1)' : 'transparent'
            }}
          >
            {widget.icon}
          </button>

          <AnimatePresence>
            {expandedWidget === widget.id && (
              <motion.div
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="absolute bottom-full right-0 mb-2 rounded-2xl z-50"
                style={{
                  background: `
                    linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%),
                    linear-gradient(135deg, rgba(99, 102, 241, 0.18) 0%, rgba(139, 92, 246, 0.14) 50%, rgba(120, 120, 140, 0.12) 100%)
                  `,
                  border: '1px solid rgba(255,255,255,0.2)',
                  boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)',
                  backdropFilter: 'blur(20px)'
                }}
              >
                {widget.content}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      ))}
    </div>
  )
}
