'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCollapseState } from '@/lib/stores/window-store'
import { useFloatingWindowStore } from '@/lib/stores/floating-window-store'
import Image from 'next/image'

interface CollapsedQuickAccessProps {
  onAppLaunch?: (appName: string) => void
}

// ZiExplorer Panel - EXACTLY like middle section (same size, same style)
export function CollapsedQuickAccess({ onAppLaunch }: CollapsedQuickAccessProps) {
  const { shouldCollapse } = useCollapseState()
  const { openWindow } = useFloatingWindowStore()

  if (!shouldCollapse) return null

  const handleZiExplorerClick = () => {
    // Open in original desktop position (top-left where ZiExplorer panel was)
    // top-24 = 96px, left-12 = 48px, w-80 = 320px, h-96 = 384px
    openWindow({
      id: 'ziexplorer',
      title: 'ZiExplorer',
      content: 'ziexplorer',
      position: { x: 48, y: 96 },
      size: { width: 320, height: 384 },
      windowType: 'panel'
    })
  }

  const apps = [
    { id: 'zinote', name: 'ZiNote', icon: '/images/icons/Zinote.png' },
    { id: 'ziboard', name: 'ZiBoard', icon: '/images/icons/ziboard.png' },
    { id: 'ziimager', name: 'Ziimager', icon: '/images/icons/Ziimager.png' },
    { id: 'zicalculator', name: 'ZiCalculator', icon: '/images/icons/zicalculator.png' }
  ]

  return (
    <div className="relative rounded-2xl px-4 py-2">
      {/* EXACT Middle Section Style - Same as Center Section */}
      <div
        style={{
          background: `
            linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
            linear-gradient(135deg, rgba(99, 102, 241, 0.32) 0%, rgba(139, 92, 246, 0.28) 25%, rgba(110, 140, 160, 0.26) 50%, rgba(90, 150, 140, 0.24) 75%, rgba(59, 130, 246, 0.22) 100%)
          `,
          border: '1px solid rgba(255,255,255,0.15)',
          boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.2), 0 4px 8px rgba(0,0,0,0.1)',
          overflow: 'visible' // Allow icons to scale outside
        }}
        className="rounded-2xl"
      >
        {/* ZiExplorer Icon - Opens floating window */}
        <div
          className="flex items-center space-x-3 px-4 py-2"
          style={{
            overflow: 'visible', // Critical: Allow icons to scale outside
            zIndex: 10 // Above background
          }}
        >
          <div className="relative">
            <button
              onClick={handleZiExplorerClick}
              className="relative w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-300 ease-out overflow-visible"
              style={{
                background: `
                  radial-gradient(circle at center,
                    rgba(0,0,0,0.85) 0%,
                    rgba(20,20,30,0.90) 30%,
                    rgba(30,30,40,0.95) 60%,
                    rgba(15,15,25,0.88) 100%
                  ),
                  radial-gradient(circle at 30% 30%, rgba(255,255,255,0.20) 0%, transparent 60%),
                  radial-gradient(circle at 70% 70%, rgba(255,255,255,0.12) 0%, transparent 50%),
                  linear-gradient(135deg,
                    rgba(99, 102, 241, 0.28) 0%,
                    rgba(139, 92, 246, 0.25) 25%,
                    rgba(110, 140, 160, 0.23) 50%,
                    rgba(90, 150, 140, 0.21) 75%,
                    rgba(59, 130, 246, 0.19) 100%
                  )
                `,
                border: '1px solid rgba(255,255,255,0.2)',
                borderTopColor: 'rgba(255,255,255,0.35)',
                borderLeftColor: 'rgba(255,255,255,0.3)',
                borderBottomColor: 'rgba(255,255,255,0.15)',
                borderRightColor: 'rgba(255,255,255,0.2)',
                boxShadow: `
                  inset 0 1px 1px rgba(255,255,255,0.4),
                  inset 0 -1px 1px rgba(0,0,0,0.2),
                  inset 1px 0 1px rgba(255,255,255,0.25),
                  inset -1px 0 1px rgba(0,0,0,0.15),
                  0 4px 16px rgba(0,0,0,0.4),
                  0 2px 8px rgba(0,0,0,0.25),
                  0 0 20px rgba(255,255,255,0.08)
                `,
                backdropFilter: 'blur(25px) saturate(1.3) brightness(1.1)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.25) translateY(-6px)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1) translateY(0)'
              }}
              title="ZiExplorer"
            >
              <div className="w-10 h-10 relative">
                <Image
                  src="/images/icons/documents-folder.png"
                  alt="ZiExplorer"
                  width={40}
                  height={40}
                  className="object-contain"
                />
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// ZiWidgets Panel - Apple 26 style system icons, positioned close to time
export function CollapsedZiWidgets() {
  const { shouldCollapse } = useCollapseState()
  const { openWindow } = useFloatingWindowStore()
  const [currentTime, setCurrentTime] = useState(new Date())
  const [expandedWidget, setExpandedWidget] = useState<string | null>(null)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  if (!shouldCollapse) return null

  const handleZiWidgetsClick = () => {
    // Open in EXACT original desktop position from DesktopWidgets
    // Original position: { x: window.innerWidth - 420, y: 100 }
    // Original size: { width: 350, height: window.innerHeight - 280 }
    // Ensure it stays within desktop bounds and doesn't slide
    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight
    const panelWidth = 350
    const panelHeight = screenHeight - 280

    // Calculate exact position to align with desktop edge (not taskbar)
    const originalX = screenWidth - 420 // Exact original position
    const originalY = 100 // Exact original Y position

    // Ensure the window doesn't go outside desktop bounds
    const safeX = Math.max(20, Math.min(originalX, screenWidth - panelWidth - 20))
    const safeY = Math.max(20, originalY)
    const safeHeight = Math.min(panelHeight, screenHeight - 120) // Leave space for taskbar

    openWindow({
      id: 'ziwidgets',
      title: 'ZiWidgets',
      content: 'ziwidgets',
      position: { x: safeX, y: safeY },
      size: { width: panelWidth, height: safeHeight },
      windowType: 'panel'
    })
  }

  const widgets = [
    {
      id: 'notes',
      label: 'Quick Notes',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
          <polyline points="14.5,2 14.5,8 20.5,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
        </svg>
      ),
      content: (
        <div className="p-4 w-64">
          <h4 className="text-white text-sm font-medium mb-3 drop-shadow-lg">Quick Notes</h4>
          <textarea
            placeholder="Jot down your thoughts..."
            className="w-full h-24 bg-black/20 border border-white/30 rounded-xl p-3 text-white placeholder-white/60 resize-none focus:outline-none focus:ring-2 focus:ring-purple-400/50 text-sm backdrop-blur-sm"
            style={{
              boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.2)'
            }}
          />
        </div>
      )
    },
    {
      id: 'weather',
      label: 'Weather',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <path d="M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z"/>
        </svg>
      ),
      content: (
        <div className="p-4 text-center min-w-[150px]">
          <div className="text-lg font-semibold text-white mb-1 drop-shadow-lg">22°C</div>
          <div className="text-xs text-white/80 drop-shadow-md">Partly Cloudy</div>
        </div>
      )
    },
    {
      id: 'wallpaper',
      label: 'Wallpaper',
      icon: (
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="8.5" cy="8.5" r="1.5"/>
          <polyline points="21,15 16,10 5,21"/>
        </svg>
      ),
      content: (
        <div className="p-4 text-center min-w-[120px]">
          <div className="text-sm text-white font-medium mb-2 drop-shadow-lg">Wallpaper</div>
          <div className="text-xs text-white/80 drop-shadow-md">Quick Settings</div>
        </div>
      )
    }
  ]

  return (
    <div className="flex items-center space-x-2 mr-4">
      {/* ZiWidgets Icon - Apple 26 Style */}
      <div className="flex items-center space-x-1">
        <button
          onClick={handleZiWidgetsClick}
          className="w-7 h-7 rounded-lg flex items-center justify-center transition-all duration-200 hover:scale-110 hover:bg-white/8 group text-white/75 hover:text-white/95"
          title="ZiWidgets"
          style={{
            background: 'transparent',
            backdropFilter: 'none'
          }}
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="3" width="7" height="7"/>
            <rect x="14" y="3" width="7" height="7"/>
            <rect x="14" y="14" width="7" height="7"/>
            <rect x="3" y="14" width="7" height="7"/>
          </svg>
        </button>
      </div>
    </div>
  )
}
