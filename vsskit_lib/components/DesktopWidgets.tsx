'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { LiquidGlassPanel, AdaptiveLiquidGlassPanel } from '@/components/LiquidGlass'
import { useWallpaperStore } from '@/lib/stores/wallpaper-store'
import { useCollapseState } from '@/lib/stores/window-store'

interface DesktopWidgetsProps {
  isInitialized: boolean
  isVisible?: boolean
  isMobile?: boolean
  isLowEndDevice?: boolean
}

interface Widget {
  id: string
  type: 'clock' | 'calendar' | 'notes' | 'weather' | 'todo' | 'wallpaper'
  position: { x: number; y: number }
  size: { width: number; height: number }
  isVisible: boolean
}

export function DesktopWidgets({
  isInitialized,
  isVisible = true,
  isMobile = false,
  isLowEndDevice = false
}: DesktopWidgetsProps) {
  const [widgets, setWidgets] = useState<Widget[]>([])
  const [currentTime, setCurrentTime] = useState(new Date())
  const [notes, setNotes] = useState('')
  const { wallpapers, currentWallpaper, setWallpaper, addCustomWallpaper, deleteWallpaper } = useWallpaperStore()
  const [showWallpaperGrid, setShowWallpaperGrid] = useState(false)

  // Collapse state for dynamic panel management
  const { shouldCollapse } = useCollapseState()

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // Initialize default widgets with mobile optimization
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined') {
      const defaultWidgets: Widget[] = [
        {
          id: 'widget-frame',
          type: 'weather',
          position: isMobile
            ? { x: 10, y: 80 } // Mobile: top-left corner
            : { x: window.innerWidth - 420, y: 100 }, // Desktop: top-right
          size: isMobile
            ? { width: Math.min(320, window.innerWidth - 20), height: Math.min(400, window.innerHeight - 200) } // Mobile: smaller, responsive
            : { width: 350, height: window.innerHeight - 280 }, // Desktop: original size
          isVisible: true
        }
      ]
      setWidgets(defaultWidgets)

      // Wallpapers are now managed by the wallpaper store
    }
  }, [isInitialized, isMobile])

  const handleWallpaperImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string
        if (imageUrl) {
          // Add imported wallpaper to store
          const newWallpaper = {
            id: `imported-${Date.now()}`,
            name: file.name.replace(/\.[^/.]+$/, ''),
            path: imageUrl,
            category: 'imported'
          }

          // Add to store and set as current wallpaper
          addCustomWallpaper(newWallpaper)
          setWallpaper(newWallpaper.id)

          console.log('Imported and set wallpaper:', newWallpaper)
        }
      }
      reader.readAsDataURL(file)
    }

    // Reset the input so the same file can be selected again
    event.target.value = ''
  }

  const handleWallpaperSelect = (wallpaperId: string) => {
    setWallpaper(wallpaperId)
  }

  const handleWallpaperDelete = (wallpaperId: string, event: React.MouseEvent) => {
    event.stopPropagation() // Prevent wallpaper selection when clicking delete
    if (confirm('Are you sure you want to delete this wallpaper?')) {
      deleteWallpaper(wallpaperId)
    }
  }



  const renderWidget = (widget: Widget) => {
    const baseClasses = "absolute bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl overflow-hidden"
    
    switch (widget.type) {
      case 'weather':
        return (
          <div
            key={widget.id}
            className="absolute pointer-events-auto"
            style={{
              left: widget.position.x,
              top: widget.position.y,
              width: widget.size.width,
              height: widget.size.height
            }}
          >
            {/* Enhanced Adaptive Liquid Glass Widget Frame */}
            <AdaptiveLiquidGlassPanel
              className="w-full h-full rounded-3xl overflow-hidden"
              intensity={1.3}
            >
              <div className="p-6 h-full flex flex-col">
                <h3 className="text-white text-lg font-semibold mb-6 text-center bg-gradient-to-r from-purple-300 to-cyan-300 bg-clip-text text-transparent drop-shadow-lg">
                  ZiWidgets
                </h3>

                <div className="flex-1 overflow-y-auto space-y-4 pr-2 custom-scrollbar">
                  {/* Time Display */}
                  <div
                    className="rounded-2xl p-4 border"
                    style={{
                      background: `
                        linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
                        linear-gradient(135deg, rgba(99, 102, 241, 0.35) 0%, rgba(139, 92, 246, 0.25) 100%)
                      `,
                      border: '1px solid rgba(255,255,255,0.2)',
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
                    }}
                  >
                    <h4 className="text-white text-sm font-medium mb-2 drop-shadow-md">Current Time</h4>
                    <div className="text-2xl font-bold text-white mb-1 drop-shadow-lg">
                      {currentTime.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                      })}
                    </div>
                    <div className="text-sm text-white/80 drop-shadow-md">
                      {currentTime.toLocaleDateString([], {
                        weekday: 'long',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </div>
                  </div>

                  {/* Quick Notes */}
                  <div
                    className="rounded-2xl p-4"
                    style={{
                      background: `
                        linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
                        linear-gradient(135deg, rgba(99, 102, 241, 0.35) 0%, rgba(139, 92, 246, 0.25) 100%)
                      `,
                      border: '1px solid rgba(255,255,255,0.2)',
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
                    }}
                  >
                    <h4 className="text-white text-sm font-medium mb-3 drop-shadow-md">Quick Notes</h4>
                    <textarea
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Jot down your thoughts..."
                      className="w-full h-16 bg-black/20 border border-white/30 rounded-xl p-3 text-white placeholder-white/60 resize-none focus:outline-none focus:ring-2 focus:ring-purple-400/50 text-sm backdrop-blur-sm"
                      style={{
                        boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.2)'
                      }}
                    />
                  </div>

                  {/* Wallpaper Settings */}
                  <div
                    className="rounded-2xl p-4"
                    style={{
                      background: `
                        linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
                        linear-gradient(135deg, rgba(99, 102, 241, 0.35) 0%, rgba(139, 92, 246, 0.25) 100%)
                      `,
                      border: '1px solid rgba(255,255,255,0.2)',
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
                    }}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white text-sm font-medium drop-shadow-md">Wallpapers</h4>
                      <button
                        onClick={() => setShowWallpaperGrid(!showWallpaperGrid)}
                        className="text-white/70 hover:text-white text-xs transition-colors"
                      >
                        {showWallpaperGrid ? '▼' : '▶'}
                      </button>
                    </div>

                    {/* Current Wallpaper Preview */}
                    <div className="mb-3">
                      <div className="text-white text-xs mb-2 opacity-80">Current:</div>
                      {(() => {
                        const current = wallpapers.find(w => w.id === currentWallpaper)
                        if (!current) return (
                          <div className="w-full h-16 rounded-lg border border-white/20 bg-gradient-to-r from-gray-600 to-gray-800 flex items-center justify-center">
                            <span className="text-white text-xs opacity-60">No wallpaper</span>
                          </div>
                        )
                        return (
                          <div
                            className="w-full h-16 rounded-lg border border-white/20 overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                            onClick={() => setShowWallpaperGrid(!showWallpaperGrid)}
                            style={{
                              background: current.path.startsWith('linear-gradient') ||
                                         current.path.startsWith('radial-gradient') ||
                                         current.path.startsWith('conic-gradient')
                                ? current.path
                                : `url('${current.path}')`,
                              backgroundSize: 'cover',
                              backgroundPosition: 'center'
                            }}
                          />
                        )
                      })()}
                      <div className="text-white text-xs mt-1 opacity-60">
                        {wallpapers.find(w => w.id === currentWallpaper)?.name || 'Default'}
                      </div>
                    </div>

                    {/* Import Wallpaper Button */}
                    <div className="mb-3">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleWallpaperImport}
                        className="hidden"
                        id="wallpaper-import"
                      />
                      <label
                        htmlFor="wallpaper-import"
                        className="w-full bg-gradient-to-r from-green-500/30 to-emerald-500/30 hover:from-green-500/40 hover:to-emerald-500/40 border border-white/30 rounded-xl p-2 text-white text-xs transition-all duration-200 backdrop-blur-sm cursor-pointer flex items-center justify-center gap-2"
                        style={{
                          boxShadow: '0 4px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
                        }}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add Wallpaper
                      </label>
                    </div>

                    {/* Wallpaper Thumbnail Grid */}
                    {showWallpaperGrid && (
                      <div className="border-t border-white/20 pt-3 mt-3">
                        <div className="grid grid-cols-3 gap-2 max-h-32 overflow-y-auto custom-scrollbar">
                          {wallpapers.slice(0, 12).map((wallpaper) => (
                            <div
                              key={wallpaper.id}
                              onClick={() => handleWallpaperSelect(wallpaper.id)}
                              className={`
                                relative aspect-video rounded-md overflow-hidden cursor-pointer transition-all duration-200 group
                                ${currentWallpaper === wallpaper.id
                                  ? 'ring-2 ring-blue-400 ring-opacity-80 shadow-lg'
                                  : 'hover:ring-1 hover:ring-white/50 hover:scale-105'
                                }
                              `}
                              style={{
                                background: wallpaper.path.startsWith('linear-gradient') ||
                                           wallpaper.path.startsWith('radial-gradient') ||
                                           wallpaper.path.startsWith('conic-gradient')
                                  ? wallpaper.path
                                  : `url('${wallpaper.path}')`,
                                backgroundSize: 'cover',
                                backgroundPosition: 'center'
                              }}
                            >
                              {/* Selection indicator */}
                              {currentWallpaper === wallpaper.id && (
                                <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span className="text-white text-xs">✓</span>
                                  </div>
                                </div>
                              )}

                              {/* Delete button for imported wallpapers */}
                              {wallpaper.category === 'imported' && (
                                <button
                                  onClick={(e) => handleWallpaperDelete(wallpaper.id, e)}
                                  className="absolute top-1 right-1 w-5 h-5 bg-red-500/80 hover:bg-red-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                  title="Delete wallpaper"
                                >
                                  <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              )}

                              {/* Wallpaper type indicator */}
                              <div className="absolute bottom-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                <div className="text-xs text-white bg-black/50 px-1 rounded">
                                  {wallpaper.category === 'imported' ? '📷' : '🎨'}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                        {wallpapers.length > 12 && (
                          <div className="text-center mt-2">
                            <span className="text-white/60 text-xs">+{wallpapers.length - 12} more</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Weather Widget */}
                  <div
                    className="rounded-2xl p-4"
                    style={{
                      background: `
                        linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
                        linear-gradient(135deg, rgba(99, 102, 241, 0.35) 0%, rgba(139, 92, 246, 0.25) 100%)
                      `,
                      border: '1px solid rgba(255,255,255,0.2)',
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
                    }}
                  >
                    <h4 className="text-white text-sm font-medium mb-3 drop-shadow-md">Weather</h4>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-lg font-semibold text-white drop-shadow-lg">22°C</div>
                        <div className="text-xs text-white/80 drop-shadow-md">Partly Cloudy</div>
                      </div>
                      <div className="text-2xl drop-shadow-lg">⛅</div>
                    </div>
                  </div>
                </div>
              </div>
            </AdaptiveLiquidGlassPanel>
          </div>
        )



      default:
        return null
    }
  }

  if (!isInitialized || !isVisible || shouldCollapse) {
    return null
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-30">
      <AnimatePresence>
        {widgets.filter(widget => widget.isVisible).map(renderWidget)}
      </AnimatePresence>



      {/* Custom styles for scrollbars and other elements */}
      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, rgba(139,69,255,0.6) 0%, rgba(59,130,246,0.6) 100%);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, rgba(139,69,255,0.8) 0%, rgba(59,130,246,0.8) 100%);
        }
      `}</style>
    </div>
  )
}
