'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { useAppStore } from '@/lib/stores/app-store'
import { getDeviceAppropriateWindowSize } from '@/lib/utils'
import { useAdaptiveLiquidGlass } from '@/hooks/useLiquidGlass'
import { useAdaptiveTheme } from '@/hooks/useAdaptiveTheme'
import { useTouchButton, useMobileInteractions } from '@/lib/touch-gestures'

interface App {
  id: string
  name: string
  icon: string
  url: string
  isPlaceholder?: boolean // For apps that aren't connected yet
  description?: string // Optional description for tooltips
}

// Expandable app configuration - easy to add more apps
const defaultApps: App[] = [
  {
    id: 'zinote',
    name: 'ZiNote',
    icon: '/images/icons/Zinote.png',
    url: '#',
    isPlaceholder: true,
    description: 'Smart note-taking and document management'
  },
  {
    id: 'ziboard',
    name: '<PERSON><PERSON><PERSON>oard',
    icon: '/images/icons/ziboard.png',
    url: '#',
    isPlaceholder: true,
    description: 'Interactive whiteboard and collaboration tool'
  },
  {
    id: 'ziimager',
    name: 'Ziimager',
    icon: '/images/icons/Ziimager.png',
    url: '#',
    isPlaceholder: true,
    description: 'AI-powered image creation and editing'
  },
  {
    id: 'zicalculator',
    name: 'ZiCalculator',
    icon: '/images/icons/zicalculator.png',
    url: '#',
    isPlaceholder: true,
    description: 'Advanced scientific calculator with study features'
  }
  // Easy to add more apps here:
  // {
  //   id: 'new-app',
  //   name: 'New App',
  //   icon: '/images/icons/new-app.png',
  //   url: '/apps/new-app',
  //   isPlaceholder: false,
  //   description: 'Description of new app'
  // }
]

export function AppDock() {
  const { closeDock, addWindow, isDockOpen, isFilterPanelOpen } = useAppStore()
  const [isDragging, setIsDragging] = useState(false)
  const [windowSize, setWindowSize] = useState({ width: 800, height: 600 })

  // Adaptive theme and liquid glass configuration for dock
  const { theme } = useAdaptiveTheme()
  const liquidGlass = useAdaptiveLiquidGlass()

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight })

      const handleResize = () => {
        setWindowSize({ width: window.innerWidth, height: window.innerHeight })
      }

      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Listen for wallpaper changes to trigger adaptive theme updates
  useEffect(() => {
    const handleWallpaperChange = (event: CustomEvent) => {
      console.log('🎨 AppDock: Wallpaper changed, triggering adaptive theme update', event.detail)
      // The adaptive theme system will automatically update through its own listeners
      // This is just for logging and potential future enhancements
    }

    window.addEventListener('wallpaperChanged', handleWallpaperChange as EventListener)
    return () => {
      window.removeEventListener('wallpaperChanged', handleWallpaperChange as EventListener)
    }
  }, [])

  // Debug adaptive theme
  useEffect(() => {
    if (theme) {
      console.log('🎨 AppDock: Current adaptive theme:', {
        mode: theme.mode,
        colors: theme.colors,
        tinting: theme.tinting,
        wallpaperColors: theme.wallpaperColors
      })
    }
  }, [theme])

  // Comprehensive dynamic closing: Close AppDock when any other section opens
  useEffect(() => {
    if (!isDockOpen) return

    // Close when filter panel opens
    if (isFilterPanelOpen) {
      closeDock()
      return
    }

    const handleDynamicClose = () => {
      if (isDockOpen) {
        closeDock()
      }
    }

    // Listen for various UI elements that should close the dock
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node instanceof HTMLElement) {
            // SpotlightSearch backdrop (z-index 2000)
            if (node.classList.contains('fixed') && node.style.zIndex === '2000') {
              handleDynamicClose()
            }
            // ZiAssist sidebar or window
            if (node.id === 'ziassist-sidebar' || node.id === 'ziassist-window') {
              handleDynamicClose()
            }
            // Any modal or overlay with high z-index
            if (node.classList.contains('fixed') &&
                (node.style.zIndex === '1000' || parseInt(node.style.zIndex) > 1000)) {
              handleDynamicClose()
            }
            // App windows or dialogs
            if (node.classList.contains('app-window') ||
                node.classList.contains('dialog') ||
                node.classList.contains('modal')) {
              handleDynamicClose()
            }
          }
        })
      })
    })

    // Listen for keyboard shortcuts that might open other sections
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K for search
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        handleDynamicClose()
      }
      // Escape key (might open other sections)
      if (e.key === 'Escape') {
        // Don't close dock on escape, let it handle its own closing
        return
      }
    }

    observer.observe(document.body, { childList: true, subtree: true })
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      observer.disconnect()
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isDockOpen, isFilterPanelOpen, closeDock])

  // Don't render if dock is not open
  if (!isDockOpen) {
    return null
  }

  const handleAppLaunch = (app: App) => {
    // Always close dock when any app is launched (including placeholders)
    closeDock()

    // Handle placeholder apps
    if (app.isPlaceholder || app.url === '#') {
      console.log(`${app.name} is not yet connected. This is a placeholder.`)
      // You can show a toast notification here in the future
      // toast.info(`${app.name} will be available soon!`)
      return
    }

    const windowSize = getDeviceAppropriateWindowSize()
    const container = document.querySelector('.epub-container') || document.body
    const bounds = container.getBoundingClientRect()

    const initialLeft = Math.max(
      5,
      Math.min(bounds.width - windowSize.width - 5, (bounds.width - windowSize.width) / 2)
    )
    const initialTop = Math.max(
      5,
      Math.min(bounds.height - windowSize.height - 5, (bounds.height - windowSize.height) / 2)
    )

    addWindow({
      title: app.name,
      appName: app.id,
      position: { x: initialLeft, y: initialTop },
      size: windowSize,
      isMinimized: false,
      isMaximized: false
    })
  }

  if (!isDockOpen) return null

  return (
    <div
      className="fixed left-1/2 transform -translate-x-1/2 z-[1010]"
      style={{
        top: '80px',
        overflow: 'visible', // Critical: Allow all child elements to scale outside
        zIndex: 1010 // Ensure proper stacking context
      }}
    >
      {/* Header with Centered ZiApps and Positioned Close Button */}
      <div
        className="relative flex flex-col items-center mb-2"
        style={{ overflow: 'visible' }} // Ensure header doesn't clip child elements
      >
        {/* Centered ZiApps Label with Icon - No Background */}
        <div className="flex items-center gap-2 mb-1">
          <svg
            className="w-5 h-5 text-lime-400 opacity-90"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            {/* ZiApps icon - Professional app grid with circles */}
            <circle cx="6.5" cy="6.5" r="3.5" fill="currentColor" />
            <circle cx="17.5" cy="6.5" r="3.5" fill="currentColor" />
            <circle cx="6.5" cy="17.5" r="3.5" fill="currentColor" />
            <circle cx="17.5" cy="17.5" r="3.5" fill="currentColor" />
          </svg>
          <h2 className="text-white font-semibold text-base" style={{ textShadow: '0 2px 4px rgba(0,0,0,0.7)' }}>
            ZiApps
          </h2>
        </div>

        {/* Enhanced Close Button with Liquid Glass - Better Visibility */}
        <button
          onClick={closeDock}
          className="absolute top-0 right-0 w-8 h-8 rounded-full transition-all duration-300 flex items-center justify-center group"
          style={{
            background: `
              radial-gradient(circle at center,
                rgba(0,0,0,0.85) 0%,
                rgba(20,20,30,0.90) 30%,
                rgba(30,30,40,0.95) 60%,
                rgba(15,15,25,0.88) 100%
              ),
              radial-gradient(circle at 30% 30%, rgba(255,255,255,0.20) 0%, transparent 60%),
              radial-gradient(circle at 70% 70%, rgba(255,255,255,0.12) 0%, transparent 50%),
              linear-gradient(135deg,
                rgba(239,68,68,0.15) 0%,
                rgba(239,68,68,0.25) 50%,
                rgba(239,68,68,0.20) 100%
              )
            `,
            border: '1px solid rgba(255,255,255,0.2)',
            borderTopColor: 'rgba(255,255,255,0.35)',
            borderLeftColor: 'rgba(255,255,255,0.3)',
            borderBottomColor: 'rgba(255,255,255,0.15)',
            borderRightColor: 'rgba(255,255,255,0.2)',
            boxShadow: `
              inset 0 1px 1px rgba(255,255,255,0.4),
              inset 0 -1px 1px rgba(0,0,0,0.2),
              inset 1px 0 1px rgba(255,255,255,0.25),
              inset -1px 0 1px rgba(0,0,0,0.15),
              0 4px 16px rgba(0,0,0,0.4),
              0 2px 8px rgba(0,0,0,0.25),
              0 0 20px rgba(239,68,68,0.2),
              0 0 40px rgba(255,255,255,0.08)
            `,
            backdropFilter: 'blur(25px) saturate(1.3) brightness(1.1)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = `
              radial-gradient(circle at center,
                rgba(0,0,0,0.90) 0%,
                rgba(25,25,35,0.95) 30%,
                rgba(35,35,45,1) 60%,
                rgba(20,20,30,0.92) 100%
              ),
              radial-gradient(circle at 30% 30%, rgba(255,255,255,0.25) 0%, transparent 60%),
              radial-gradient(circle at 70% 70%, rgba(255,255,255,0.15) 0%, transparent 50%),
              linear-gradient(135deg,
                rgba(239,68,68,0.4) 0%,
                rgba(239,68,68,0.6) 50%,
                rgba(239,68,68,0.5) 100%
              )
            `
            e.currentTarget.style.boxShadow = `
              inset 0 1px 1px rgba(255,255,255,0.4),
              inset 0 -1px 1px rgba(0,0,0,0.2),
              inset 1px 0 1px rgba(255,255,255,0.25),
              inset -1px 0 1px rgba(0,0,0,0.15),
              0 4px 16px rgba(0,0,0,0.4),
              0 2px 8px rgba(0,0,0,0.25),
              0 0 25px rgba(239,68,68,0.7),
              0 0 50px rgba(239,68,68,0.5),
              0 0 75px rgba(239,68,68,0.4),
              0 0 100px rgba(255,0,0,0.3)
            `
            e.currentTarget.style.borderColor = 'rgba(239,68,68,0.6)'
            e.currentTarget.style.borderTopColor = 'rgba(239,68,68,0.8)'
            e.currentTarget.style.borderLeftColor = 'rgba(239,68,68,0.7)'
            e.currentTarget.style.borderBottomColor = 'rgba(239,68,68,0.4)'
            e.currentTarget.style.borderRightColor = 'rgba(239,68,68,0.5)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = `
              radial-gradient(circle at center,
                rgba(0,0,0,0.85) 0%,
                rgba(20,20,30,0.90) 30%,
                rgba(30,30,40,0.95) 60%,
                rgba(15,15,25,0.88) 100%
              ),
              radial-gradient(circle at 30% 30%, rgba(255,255,255,0.20) 0%, transparent 60%),
              radial-gradient(circle at 70% 70%, rgba(255,255,255,0.12) 0%, transparent 50%),
              linear-gradient(135deg,
                rgba(239,68,68,0.15) 0%,
                rgba(239,68,68,0.25) 50%,
                rgba(239,68,68,0.20) 100%
              )
            `
            e.currentTarget.style.boxShadow = `
              inset 0 1px 1px rgba(255,255,255,0.4),
              inset 0 -1px 1px rgba(0,0,0,0.2),
              inset 1px 0 1px rgba(255,255,255,0.25),
              inset -1px 0 1px rgba(0,0,0,0.15),
              0 4px 16px rgba(0,0,0,0.4),
              0 2px 8px rgba(0,0,0,0.25),
              0 0 20px rgba(239,68,68,0.2),
              0 0 40px rgba(255,255,255,0.08)
            `
            e.currentTarget.style.borderColor = 'rgba(255,255,255,0.2)'
            e.currentTarget.style.borderTopColor = 'rgba(255,255,255,0.35)'
            e.currentTarget.style.borderLeftColor = 'rgba(255,255,255,0.3)'
            e.currentTarget.style.borderBottomColor = 'rgba(255,255,255,0.15)'
            e.currentTarget.style.borderRightColor = 'rgba(255,255,255,0.2)'
          }}
        >
          {/* Liquid Glass Overlay */}
          <div
            className="absolute inset-0 rounded-full pointer-events-none"
            style={{
              background: `
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.15) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.08) 0%, transparent 40%),
                linear-gradient(135deg,
                  rgba(255,255,255,0.10) 0%,
                  rgba(255,255,255,0.05) 50%,
                  transparent 100%
                )
              `,
              border: '1px solid rgba(255,255,255,0.12)',
              borderTopColor: 'rgba(255,255,255,0.20)',
              borderLeftColor: 'rgba(255,255,255,0.16)',
              borderBottomColor: 'rgba(255,255,255,0.06)',
              borderRightColor: 'rgba(255,255,255,0.08)'
            }}
          />

          <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            className="text-white group-hover:text-white/95 transition-colors relative z-10"
            style={{
              filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.6))'
            }}
          >
            <path
              d="M3 3L11 11M3 11L11 3"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>

      {/* Compact App Dock Frame */}
      <motion.div
        id="app-dock"
        className="ios-taskbar-dock"
        style={{
          overflow: 'visible', // Critical: Allow all child elements to scale outside
          zIndex: 1010 // Ensure proper stacking
        }}
        drag
        dragMomentum={false}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={() => setIsDragging(false)}
        dragConstraints={{
          left: -windowSize.width / 2 + 150,
          right: windowSize.width / 2 - 150,
          top: -windowSize.height / 2 + 100,
          bottom: windowSize.height / 2 - 100
        }}
        initial={{
          scale: 0.3,
          opacity: 0,
          y: 30,
          scaleY: 0.2, // Gentle compression like soft liquid
          scaleX: 0.4, // Mild horizontal compression
          borderRadius: "40px", // Moderately rounded during compression
          rotateX: 5, // Very subtle 3D tilt
          rotateY: 2,
          filter: 'blur(6px) brightness(0.85) contrast(0.9)',
          backdropFilter: 'blur(20px) saturate(0.8) brightness(0.95)'
        }}
        animate={{
          scale: 1,
          opacity: 1,
          y: 0,
          scaleY: 1, // Gentle expansion to normal height
          scaleX: 1, // Smooth expansion to normal width
          borderRadius: "24px", // Returns to normal border radius
          rotateX: 0, // Returns to flat orientation
          rotateY: 0,
          filter: 'blur(0px) brightness(1.02) contrast(1.05)',
          backdropFilter: 'blur(30px) saturate(1.4) brightness(1.05)'
        }}
        exit={{
          scale: 0.3,
          opacity: 0,
          y: 30,
          scaleY: 0.2, // Gentle compression back to soft liquid
          scaleX: 0.4, // Mild horizontal compression
          borderRadius: "40px", // Moderately rounded during compression
          rotateX: 5, // Very subtle 3D tilt
          rotateY: 2,
          filter: 'blur(6px) brightness(0.85) contrast(0.9)',
          backdropFilter: 'blur(20px) saturate(0.8) brightness(0.95)'
        }}
        transition={{
          type: "spring",
          stiffness: 250,
          damping: 28,
          duration: 1.0,
          // Harmonious Apple 26 liquid glass with calming timing
          scale: {
            type: "spring",
            stiffness: 220,
            damping: 25,
            duration: 0.8,
            bounce: 0.15
          },
          scaleY: {
            type: "spring",
            stiffness: 280,
            damping: 22,
            duration: 0.7,
            bounce: 0.2
          },
          scaleX: {
            type: "spring",
            stiffness: 260,
            damping: 24,
            duration: 0.75,
            bounce: 0.18
          },
          borderRadius: {
            type: "spring",
            stiffness: 180,
            damping: 26,
            duration: 0.6
          },
          rotateX: {
            type: "spring",
            stiffness: 200,
            damping: 30,
            duration: 0.5
          },
          rotateY: {
            type: "spring",
            stiffness: 200,
            damping: 30,
            duration: 0.5
          },
          filter: {
            type: "spring",
            stiffness: 150,
            damping: 32,
            duration: 0.9
          },
          backdropFilter: {
            type: "spring",
            stiffness: 160,
            damping: 30,
            duration: 0.85
          }
        }}
      >
        {/* Apps Taskbar - Enhanced Liquid Glass with Vibrant Colors + Adaptive Fallback */}
        <div className="flex items-center gap-3 px-3 py-1.5 relative"
          style={{
            overflow: 'visible', // Critical: Allow icons to scale outside container
            zIndex: 10, // Ensure container is properly layered
            background: liquidGlass.isLowEnd ?
              // Fallback for low-end devices - still colorful but simpler
              `
                radial-gradient(circle at center,
                  rgba(15,15,35,0.85) 0%,
                  rgba(25,25,45,0.90) 50%,
                  rgba(20,20,40,0.88) 100%
                ),
                radial-gradient(circle at 25% 25%, rgba(147, 51, 234, 0.3) 0%, transparent 60%),
                radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.25) 0%, transparent 55%)
              ` :
              // Apple 26 background with subtle bluish-purplish tint for readability
              theme?.tinting?.blend ? `
                ${theme.tinting.blend},
                linear-gradient(135deg,
                  rgba(255,255,255,0.12) 0%,
                  rgba(255,255,255,0.07) 30%,
                  rgba(255,255,255,0.05) 60%,
                  rgba(255,255,255,0.09) 100%
                ),
                linear-gradient(135deg,
                  rgba(99, 102, 241, 0.08) 0%,
                  rgba(139, 92, 246, 0.06) 50%,
                  rgba(59, 130, 246, 0.04) 100%
                ),
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.08) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.06) 0%, transparent 40%),
                radial-gradient(circle at 50% 10%, rgba(147, 51, 234, 0.08) 0%, transparent 35%),
                radial-gradient(circle at 10% 50%, rgba(79, 70, 229, 0.06) 0%, transparent 30%),
                radial-gradient(circle at 90% 50%, rgba(59, 130, 246, 0.07) 0%, transparent 32%)
              ` : `
                linear-gradient(135deg,
                  rgba(255,255,255,0.12) 0%,
                  rgba(255,255,255,0.07) 30%,
                  rgba(255,255,255,0.05) 60%,
                  rgba(255,255,255,0.09) 100%
                ),
                linear-gradient(135deg,
                  rgba(99, 102, 241, 0.08) 0%,
                  rgba(139, 92, 246, 0.06) 50%,
                  rgba(59, 130, 246, 0.04) 100%
                ),
                radial-gradient(circle at 25% 25%, rgba(255,255,255,0.08) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255,255,255,0.06) 0%, transparent 40%),
                radial-gradient(circle at 50% 10%, rgba(147, 51, 234, 0.08) 0%, transparent 35%),
                radial-gradient(circle at 10% 50%, rgba(79, 70, 229, 0.06) 0%, transparent 30%),
                radial-gradient(circle at 90% 50%, rgba(59, 130, 246, 0.07) 0%, transparent 32%)
              `,
            borderRadius: '24px',
            border: '1px solid rgba(255,255,255,0.28)',
            borderTop: '1px solid rgba(255,255,255,0.35)',
            borderLeft: '1px solid rgba(255,255,255,0.32)',
            borderBottom: '0.5px solid rgba(255,255,255,0.22)',
            borderRight: '0.5px solid rgba(255,255,255,0.25)',
            // Original dimensional shadows - between AppDock intensity and current
            boxShadow: `
              0 28px 56px rgba(0,0,0,0.38),
              0 14px 28px rgba(0,0,0,0.28),
              0 7px 14px rgba(0,0,0,0.18),
              inset 0 1px 0 rgba(255,255,255,0.35),
              inset 0 0.5px 1px rgba(255,255,255,0.25),
              inset 0 -0.5px 1px rgba(0,0,0,0.08),
              inset 1px 0 1px rgba(255,255,255,0.2),
              inset -1px 0 1px rgba(0,0,0,0.06),
              0 0 25px rgba(139,69,255,0.18),
              0 0 50px rgba(79, 70, 229, 0.12)
            `,
            // Original glass effects
            backdropFilter: 'blur(28px) saturate(1.35) brightness(1.08)',
            filter: liquidGlass.isLowEnd ?
              'brightness(1.05) contrast(1.05) saturate(1.2)' :
              'brightness(1.12) contrast(1.10) saturate(1.4)',
            transform: liquidGlass.config.useGPUAcceleration ? 'translateZ(0)' : undefined,
            transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 2.2)'
          }}
        >
          {/* Enhanced Liquid Glass Overlay for Dimension with Adaptive Enhancement */}
          <div
            className="absolute inset-0 pointer-events-none"
            style={{
              borderRadius: '18px',
              background: liquidGlass.isLowEnd ?
                // Simplified overlay for low-end devices
                `
                  radial-gradient(circle at 30% 20%, rgba(255,255,255,0.12) 0%, transparent 50%),
                  radial-gradient(circle at 70% 80%, rgba(255,255,255,0.08) 0%, transparent 40%)
                ` :
                // Advanced overlay with subtle bluish-purplish tint for readability
                theme?.tinting?.overlay ? `
                  ${theme.tinting.overlay},
                  linear-gradient(135deg,
                    rgba(99, 102, 241, 0.06) 0%,
                    rgba(139, 92, 246, 0.04) 50%,
                    rgba(59, 130, 246, 0.03) 100%
                  ),
                  radial-gradient(circle at 30% 20%, rgba(255,255,255,0.15) 0%, transparent 50%),
                  radial-gradient(circle at 70% 30%, rgba(255,255,255,0.12) 0%, transparent 45%),
                  radial-gradient(circle at 20% 70%, rgba(255,255,255,0.10) 0%, transparent 40%),
                  radial-gradient(circle at 80% 80%, rgba(255,255,255,0.13) 0%, transparent 45%),
                  radial-gradient(circle at 50% 10%, rgba(147, 51, 234, 0.08) 0%, transparent 35%),
                  radial-gradient(circle at 10% 50%, rgba(79, 70, 229, 0.06) 0%, transparent 30%),
                  radial-gradient(circle at 90% 50%, rgba(59, 130, 246, 0.07) 0%, transparent 32%),
                  linear-gradient(135deg,
                    rgba(255,255,255,0.12) 0%,
                    rgba(255,255,255,0.08) 20%,
                    rgba(255,255,255,0.04) 40%,
                    transparent 60%,
                    transparent 80%,
                    rgba(255,255,255,0.06) 100%
                  ),
                  linear-gradient(45deg,
                    transparent 0%,
                    rgba(255,255,255,0.06) 30%,
                    rgba(255,255,255,0.10) 50%,
                    rgba(255,255,255,0.06) 70%,
                    transparent 100%
                  )
                ` : `
                  linear-gradient(135deg,
                    rgba(99, 102, 241, 0.06) 0%,
                    rgba(139, 92, 246, 0.04) 50%,
                    rgba(59, 130, 246, 0.03) 100%
                  ),
                  radial-gradient(circle at 30% 20%, rgba(255,255,255,0.15) 0%, transparent 50%),
                  radial-gradient(circle at 70% 30%, rgba(255,255,255,0.12) 0%, transparent 45%),
                  radial-gradient(circle at 20% 70%, rgba(255,255,255,0.10) 0%, transparent 40%),
                  radial-gradient(circle at 80% 80%, rgba(255,255,255,0.13) 0%, transparent 45%),
                  radial-gradient(circle at 50% 10%, rgba(147, 51, 234, 0.08) 0%, transparent 35%),
                  radial-gradient(circle at 10% 50%, rgba(79, 70, 229, 0.06) 0%, transparent 30%),
                  radial-gradient(circle at 90% 50%, rgba(59, 130, 246, 0.07) 0%, transparent 32%),
                  linear-gradient(135deg,
                    rgba(255,255,255,0.12) 0%,
                    rgba(255,255,255,0.08) 20%,
                    rgba(255,255,255,0.04) 40%,
                    transparent 60%,
                    transparent 80%,
                    rgba(255,255,255,0.06) 100%
                  ),
                  linear-gradient(45deg,
                    transparent 0%,
                    rgba(255,255,255,0.06) 30%,
                    rgba(255,255,255,0.10) 50%,
                    rgba(255,255,255,0.06) 70%,
                    transparent 100%
                  )
                `,
              border: liquidGlass.isLowEnd ?
                '1px solid rgba(255,255,255,0.15)' :
                '1px solid rgba(255,255,255,0.20)',
              borderTopColor: liquidGlass.isLowEnd ?
                'rgba(255,255,255,0.25)' :
                'rgba(255,255,255,0.35)',
              borderLeftColor: liquidGlass.isLowEnd ?
                'rgba(255,255,255,0.20)' :
                'rgba(255,255,255,0.30)',
              borderBottomColor: liquidGlass.isLowEnd ?
                'rgba(255,255,255,0.08)' :
                'rgba(255,255,255,0.12)',
              borderRightColor: liquidGlass.isLowEnd ?
                'rgba(255,255,255,0.12)' :
                'rgba(255,255,255,0.18)',
              boxShadow: liquidGlass.isLowEnd ?
                `
                  inset 0 1px 0 rgba(255,255,255,0.20),
                  inset 0 -1px 0 rgba(0,0,0,0.10)
                ` :
                `
                  inset 0 2px 0 rgba(255,255,255,0.30),
                  inset 0 -2px 0 rgba(0,0,0,0.12),
                  inset 1px 0 0 rgba(255,255,255,0.15),
                  inset -1px 0 0 rgba(255,255,255,0.15)
                `,
              filter: liquidGlass.config.enableDistortion ? 'url(#glass-distortion)' : undefined
            }}
          />

          {defaultApps.map((app) => (
            <AppIcon
              key={app.id}
              app={app}
              onLaunch={handleAppLaunch}
              disabled={isDragging}
            />
          ))}
        </div>
      </motion.div>
    </div>
  )
}

interface AppIconProps {
  app: App
  onLaunch: (app: App) => void
  disabled?: boolean
}

function AppIcon({ app, onLaunch, disabled }: AppIconProps) {
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const { isTouchDevice } = useMobileInteractions()

  // Touch-friendly button with haptic feedback
  const touchButtonRef = useTouchButton(
    () => {
      if (!disabled) {
        onLaunch(app)
        // Add haptic feedback on supported devices
        if ('vibrate' in navigator) {
          navigator.vibrate(50)
        }
      }
    }
  )

  // Only show placeholder when image actually fails to load
  const shouldShowPlaceholder = imageError

  return (
    <motion.button
      ref={touchButtonRef as any}
      className={`flex flex-col items-center p-3 rounded-2xl hover:bg-white/8 transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed group relative ${isTouchDevice ? 'touch-manipulation' : ''}`}
      style={{
        overflow: 'visible', // Critical: Allow child elements to scale outside
        zIndex: 1 // Base z-index for button
      }}
      onClick={() => !disabled && onLaunch(app)}
      disabled={disabled}
      onMouseEnter={() => !disabled && setIsHovered(true)}
      onMouseLeave={() => !disabled && setIsHovered(false)}
      initial={{ scale: 1 }}
      whileHover={{
        scale: disabled ? 1 : 1, // Button container stays at original size
        transition: {
          type: "spring",
          stiffness: 200,
          damping: 25,
          duration: 0.6
        }
      }}
      whileTap={{
        scale: disabled ? 1 : 0.95,
        transition: {
          type: "spring",
          stiffness: 300,
          damping: 20,
          duration: 0.3
        }
      }}
      animate={{
        scale: 1,
        transition: {
          type: "spring",
          stiffness: 180,
          damping: 22,
          duration: 0.8
        }
      }}
    >
      <motion.div
        className="w-16 h-16 mb-1.5 rounded-3xl flex items-center justify-center transition-all duration-500 relative"
        style={{
          overflow: 'visible', // Critical: Allow icon to scale outside this container
          zIndex: 2, // Higher z-index than button
          background: `
            radial-gradient(circle at center,
              rgba(255,255,255,0.15) 0%,
              rgba(255,255,255,0.08) 40%,
              rgba(255,255,255,0.05) 80%,
              rgba(255,255,255,0.02) 100%
            ),
            radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.12) 0%, transparent 60%),
            radial-gradient(circle at 70% 70%, rgba(59, 130, 246, 0.10) 0%, transparent 50%),
            linear-gradient(135deg,
              rgba(255,255,255,0.20) 0%,
              rgba(255,255,255,0.05) 100%
            )
          `,
          border: '1px solid rgba(255,255,255,0.25)',
          borderTopColor: 'rgba(255,255,255,0.35)',
          borderLeftColor: 'rgba(255,255,255,0.30)',
          borderBottomColor: 'rgba(255,255,255,0.15)',
          borderRightColor: 'rgba(255,255,255,0.20)',
          boxShadow: `
            0 6px 16px rgba(0,0,0,0.25),
            0 3px 8px rgba(0,0,0,0.15),
            inset 0 1px 0 rgba(255,255,255,0.30),
            inset 0 -1px 0 rgba(0,0,0,0.10),
            0 0 20px rgba(147, 51, 234, 0.15),
            0 0 40px rgba(59, 130, 246, 0.10)
          `,
          backdropFilter: 'blur(15px) saturate(1.3) brightness(1.1)'
        }}
        initial={{ scale: 1 }}
        whileHover={{
          scale: disabled ? 1 : 0.75, // Container scales down more dramatically - Apple 26 style
          background: `
            radial-gradient(circle at center,
              rgba(255,255,255,0.25) 0%,
              rgba(255,255,255,0.15) 40%,
              rgba(255,255,255,0.10) 80%,
              rgba(255,255,255,0.05) 100%
            ),
            radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.20) 0%, transparent 60%),
            radial-gradient(circle at 70% 70%, rgba(59, 130, 246, 0.18) 0%, transparent 50%),
            linear-gradient(135deg,
              rgba(255,255,255,0.30) 0%,
              rgba(255,255,255,0.10) 100%
            )
          `,
          boxShadow: `
            0 8px 20px rgba(0,0,0,0.3),
            0 4px 10px rgba(0,0,0,0.2),
            inset 0 1px 0 rgba(255,255,255,0.40),
            inset 0 -1px 0 rgba(0,0,0,0.15),
            0 0 30px rgba(147, 51, 234, 0.25),
            0 0 60px rgba(59, 130, 246, 0.20)
          `,
          transition: {
            type: "spring",
            stiffness: 320,
            damping: 18,
            duration: 0.25 // Synchronized with icon timing
          }
        }}
        animate={{
          scale: 1,
          background: `
            radial-gradient(circle at center,
              rgba(255,255,255,0.15) 0%,
              rgba(255,255,255,0.08) 40%,
              rgba(255,255,255,0.05) 80%,
              rgba(255,255,255,0.02) 100%
            ),
            radial-gradient(circle at 30% 30%, rgba(147, 51, 234, 0.12) 0%, transparent 60%),
            radial-gradient(circle at 70% 70%, rgba(59, 130, 246, 0.10) 0%, transparent 50%),
            linear-gradient(135deg,
              rgba(255,255,255,0.20) 0%,
              rgba(255,255,255,0.05) 100%
            )
          `,
          boxShadow: `
            0 6px 16px rgba(0,0,0,0.25),
            0 3px 8px rgba(0,0,0,0.15),
            inset 0 1px 0 rgba(255,255,255,0.30),
            inset 0 -1px 0 rgba(0,0,0,0.10),
            0 0 20px rgba(147, 51, 234, 0.15),
            0 0 40px rgba(59, 130, 246, 0.10)
          `,
          transition: {
            type: "spring",
            stiffness: 450,
            damping: 15,
            duration: 0.25 // Synchronized return timing
          }
        }}
      >
        {/* Icon Container - Using Transform to Scale Outside Bounds */}
        <motion.div
          className="w-12 h-12 flex items-center justify-center relative"
          style={{
            overflow: 'visible', // Critical: Allow scaling outside bounds
            zIndex: 20, // High z-index to appear above all containers
            transformOrigin: 'center', // Ensure scaling from center
            position: 'relative' // Maintain normal flow
          }}
          initial={{ scale: 1 }}
          whileHover={{
            scale: disabled ? 1 : 1.6, // Icon scales up dramatically to jump out of frame - Apple 26 style
            zIndex: 100, // Very high z-index when hovered to appear above everything
            transition: {
              type: "spring",
              stiffness: 380,
              damping: 12,
              duration: 0.25
            }
          }}
          animate={{
            scale: 1,
            zIndex: 20,
            transition: {
              type: "spring",
              stiffness: 450,
              damping: 15,
              duration: 0.25 // Synchronized return timing
            }
          }}
        >
          {!shouldShowPlaceholder ? (
            <div
              className="w-full h-full"
              style={{
                overflow: 'visible', // Allow image to scale outside bounds
                zIndex: 30 // Higher z-index for the actual image
              }}
            >
              <Image
                src={app.icon}
                alt={`${app.name} icon`}
                width={48}
                height={48}
                className="w-full h-full object-cover rounded-2xl"
                style={{
                  overflow: 'visible', // Critical: Allow image to scale outside
                  zIndex: 40 // Highest z-index for the actual image element
                }}
                onError={() => setImageError(true)}
                priority={false}
              />
            </div>
          ) : (
            <div className="w-full h-full rounded-2xl flex items-center justify-center relative"
              style={{
                overflow: 'visible', // Allow placeholder to scale outside bounds
                zIndex: 30, // Higher z-index for placeholder
                background: app.isPlaceholder
                  ? `
                    radial-gradient(circle at center,
                      rgba(156, 163, 175, 0.95) 0%,
                      rgba(107, 114, 128, 0.98) 40%,
                      rgba(75, 85, 99, 1) 80%,
                      rgba(55, 65, 81, 0.95) 100%
                    ),
                    radial-gradient(circle at 30% 30%, rgba(255,255,255,0.15) 0%, transparent 50%),
                    radial-gradient(circle at 70% 70%, rgba(255,255,255,0.08) 0%, transparent 40%),
                    linear-gradient(135deg,
                      rgba(249, 115, 22, 0.12) 0%,
                      rgba(251, 146, 60, 0.08) 50%,
                      rgba(253, 186, 116, 0.06) 100%
                    )
                  `
                  : `
                    radial-gradient(circle at center,
                      rgba(59, 130, 246, 0.95) 0%,
                      rgba(79, 70, 229, 0.98) 40%,
                      rgba(147, 51, 234, 1) 80%,
                      rgba(126, 34, 206, 0.95) 100%
                    ),
                    radial-gradient(circle at 30% 30%, rgba(255,255,255,0.20) 0%, transparent 50%),
                    radial-gradient(circle at 70% 70%, rgba(255,255,255,0.12) 0%, transparent 40%)
                  `,
                border: '1px solid rgba(255,255,255,0.20)',
                borderTopColor: 'rgba(255,255,255,0.30)',
                borderLeftColor: 'rgba(255,255,255,0.25)',
                borderBottomColor: 'rgba(255,255,255,0.10)',
                borderRightColor: 'rgba(255,255,255,0.15)',
                boxShadow: `
                  inset 0 1px 0 rgba(255,255,255,0.25),
                  inset 0 -1px 0 rgba(0,0,0,0.15),
                  0 2px 8px rgba(0,0,0,0.2)
                `,
                backdropFilter: 'blur(8px) saturate(1.2)'
              }}
            >
              {/* Liquid Glass Overlay for Placeholder */}
              <div
                className="absolute inset-0 rounded-2xl pointer-events-none"
                style={{
                  background: `
                    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.12) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.08) 0%, transparent 40%),
                    linear-gradient(135deg,
                      rgba(255,255,255,0.08) 0%,
                      rgba(255,255,255,0.04) 50%,
                      transparent 100%
                    )
                  `
                }}
              />

              <span className="text-white text-lg font-bold relative z-10"
                style={{
                  textShadow: '0 2px 4px rgba(0,0,0,0.6)',
                  filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.4))'
                }}
              >
                {app.name.charAt(0)}
              </span>

              {/* Enhanced Placeholder Indicator */}
              {app.isPlaceholder && (
                <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center"
                  style={{
                    background: `
                      radial-gradient(circle at center,
                        rgba(249, 115, 22, 0.95) 0%,
                        rgba(251, 146, 60, 0.98) 40%,
                        rgba(253, 186, 116, 1) 80%,
                        rgba(251, 146, 60, 0.95) 100%
                      ),
                      radial-gradient(circle at 30% 30%, rgba(255,255,255,0.25) 0%, transparent 50%)
                    `,
                    border: '1.5px solid rgba(255,255,255,0.8)',
                    boxShadow: `
                      inset 0 1px 0 rgba(255,255,255,0.4),
                      0 2px 6px rgba(0,0,0,0.3),
                      0 0 12px rgba(249, 115, 22, 0.4)
                    `,
                    backdropFilter: 'blur(4px)'
                  }}
                >
                  <div className="w-1.5 h-1.5 bg-white rounded-full"
                    style={{
                      boxShadow: '0 1px 2px rgba(0,0,0,0.3)'
                    }}
                  />
                </div>
              )}
            </div>
          )}
        </motion.div>
      </motion.div>
      <motion.span
        className="text-white text-xs text-center leading-tight font-medium opacity-90 transition-all duration-500"
        style={{ textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}
        initial={{ y: 0, opacity: 0.9 }}
        animate={
          isHovered && !disabled
            ? {
                y: [-8, -12, -8, 0], // Subtle bouncing sequence - Apple 26 style
                opacity: 0.9,
                transition: {
                  duration: 0.25, // Synchronized with icon and background timing
                  times: [0, 0.3, 0.6, 1], // Smooth effect timing
                  ease: "easeOut"
                }
              }
            : {
                y: 0, // Returns to original position when not hovered
                opacity: 0.9,
                transition: {
                  type: "spring",
                  stiffness: 450,
                  damping: 15,
                  duration: 0.25 // Synchronized return timing
                }
              }
        }
      >
        {app.name}
      </motion.span>
    </motion.button>
  )
}
