'use client'

import React, { useState, useRef } from 'react'
import Draggable from 'react-draggable'
import { Resizable } from 'react-resizable'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Minimize2, Maximize2 } from 'lucide-react'
import 'react-resizable/css/styles.css'

interface FloatingWindowProps {
  title: string
  isOpen: boolean
  onClose: () => void
  onMinimize?: () => void
  children: React.ReactNode
  defaultPosition?: { x: number; y: number }
  defaultSize?: { width: number; height: number }
  className?: string
  resizable?: boolean
}

export function FloatingWindow({
  title,
  isOpen,
  onClose,
  onMinimize,
  children,
  defaultPosition = { x: 100, y: 100 },
  defaultSize = { width: 400, height: 300 },
  className = '',
  resizable = true
}: FloatingWindowProps) {
  const [isMaximized, setIsMaximized] = useState(false)
  const [position, setPosition] = useState(defaultPosition)
  const [size, setSize] = useState(defaultSize)
  const [originalSize, setOriginalSize] = useState(defaultSize)
  const [originalPosition, setOriginalPosition] = useState(defaultPosition)
  const nodeRef = useRef(null)

  const handleMaximize = () => {
    if (isMaximized) {
      // Restore to original size and position
      setSize(originalSize)
      setPosition(originalPosition)
      setIsMaximized(false)
    } else {
      // Save current state before maximizing
      setOriginalSize(size)
      setOriginalPosition(position)
      // Maximize to full screen with padding
      setSize({ width: window.innerWidth - 40, height: window.innerHeight - 40 })
      setPosition({ x: 20, y: 20 })
      setIsMaximized(true)
    }
  }

  const handleResize = (event: any, { size: newSize }: any) => {
    if (!isMaximized) {
      setSize(newSize)
    }
  }

  const windowStyle = isMaximized
    ? {
        position: 'fixed' as const,
        top: '20px',
        left: '20px',
        right: '20px',
        bottom: '20px',
        width: 'calc(100vw - 40px)',
        height: 'calc(100vh - 40px)',
        zIndex: 1000
      }
    : {
        width: `${size.width}px`,
        height: `${size.height}px`,
        zIndex: 1000
      }

  return (
    <AnimatePresence>
      {isOpen && (
        <Draggable
          nodeRef={nodeRef}
          disabled={isMaximized}
          position={position}
          onStop={(e, data) => {
            if (!isMaximized) {
              setPosition({ x: data.x, y: data.y })
            }
          }}
          handle=".window-header"
        >
          <Resizable
            width={size.width}
            height={size.height}
            onResize={handleResize}
            minConstraints={[300, 200]}
            maxConstraints={[window?.innerWidth || 1200, window?.innerHeight || 800]}
            resizeHandles={isMaximized ? [] : ['se', 'e', 's']}
          >
            <motion.div
              ref={nodeRef}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.2 }}
              className={`fixed rounded-2xl overflow-hidden shadow-2xl ${className}`}
              style={{
                ...windowStyle,
                background: `
                  linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 50%, rgba(255,255,255,0.12) 100%),
                  linear-gradient(135deg, rgba(99, 102, 241, 0.32) 0%, rgba(139, 92, 246, 0.28) 25%, rgba(110, 140, 160, 0.26) 50%, rgba(90, 150, 140, 0.24) 75%, rgba(59, 130, 246, 0.22) 100%)
                `,
                border: '1px solid rgba(255,255,255,0.2)',
                backdropFilter: 'blur(20px) saturate(1.2) brightness(1.1)'
              }}
            >
            {/* Window Header */}
            <div
              className="window-header flex items-center justify-between p-4 cursor-move"
              style={{
                background: `
                  linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
                  linear-gradient(135deg, rgba(99, 102, 241, 0.20) 0%, rgba(139, 92, 246, 0.16) 25%, rgba(110, 140, 160, 0.14) 50%, rgba(90, 150, 140, 0.12) 75%, rgba(59, 130, 246, 0.10) 100%)
                `,
                borderBottom: '1px solid rgba(255,255,255,0.1)'
              }}
            >
              <h3 className="text-white font-semibold text-sm drop-shadow-lg">{title}</h3>
              
              <div className="flex items-center space-x-2">
                {onMinimize && (
                  <button
                    onClick={onMinimize}
                    className="w-6 h-6 rounded-full bg-yellow-500/80 hover:bg-yellow-500 flex items-center justify-center transition-colors"
                    title="Minimize"
                  >
                    <Minimize2 size={12} className="text-white" />
                  </button>
                )}
                
                <button
                  onClick={handleMaximize}
                  className="w-6 h-6 rounded-full bg-green-500/80 hover:bg-green-500 flex items-center justify-center transition-colors"
                  title={isMaximized ? "Restore" : "Maximize"}
                >
                  <Maximize2 size={12} className="text-white" />
                </button>
                
                <button
                  onClick={onClose}
                  className="w-6 h-6 rounded-full bg-red-500/80 hover:bg-red-500 flex items-center justify-center transition-colors"
                  title="Close"
                >
                  <X size={12} className="text-white" />
                </button>
              </div>
            </div>

            {/* Window Content */}
            <div className="flex-1 overflow-auto p-4" style={{ height: 'calc(100% - 64px)' }}>
              {children}
            </div>
          </motion.div>
        </Resizable>
        </Draggable>
      )}
    </AnimatePresence>
  )
}
