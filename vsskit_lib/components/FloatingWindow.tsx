'use client'

import React, { useState } from 'react'
import { Rnd } from 'react-rnd'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Minimize2, Maximize2 } from 'lucide-react'

interface FloatingWindowProps {
  title: string
  isOpen: boolean
  onClose: () => void
  onMinimize?: () => void
  children: React.ReactNode
  defaultPosition?: { x: number; y: number }
  defaultSize?: { width: number; height: number }
  className?: string
  resizable?: boolean
  windowType?: 'panel' | 'app' // New prop to distinguish panel windows from app windows
}

export function FloatingWindow({
  title,
  isOpen,
  onClose,
  onMinimize,
  children,
  defaultPosition = { x: 100, y: 100 },
  defaultSize = { width: 400, height: 300 },
  className = '',
  resizable = true,
  windowType = 'app'
}: FloatingWindowProps) {
  const [isMaximized, setIsMaximized] = useState(false)
  const [position, setPosition] = useState(defaultPosition)
  const [size, setSize] = useState(defaultSize)
  const [originalSize, setOriginalSize] = useState(defaultSize)
  const [originalPosition, setOriginalPosition] = useState(defaultPosition)

  const handleMaximize = () => {
    if (isMaximized) {
      // Restore to original size and position
      setSize(originalSize)
      setPosition(originalPosition)
      setIsMaximized(false)
    } else {
      // Save current state before maximizing
      setOriginalSize(size)
      setOriginalPosition(position)

      if (windowType === 'panel') {
        // Panel windows: maximize to reasonable size, not full screen
        const maxWidth = Math.min(800, window.innerWidth - 100)
        const maxHeight = Math.min(600, window.innerHeight - 100)
        setSize({ width: maxWidth, height: maxHeight })
        setPosition({ x: 50, y: 50 })
      } else {
        // App windows: maximize to full screen (no header offset)
        setSize({ width: window.innerWidth, height: window.innerHeight })
        setPosition({ x: 0, y: 0 })
      }
      setIsMaximized(true)
    }
  }

  // Get constraints based on window type
  const getConstraints = () => {
    if (windowType === 'panel') {
      return {
        minWidth: 300,
        minHeight: 200,
        maxWidth: Math.min(800, window.innerWidth - 50),
        maxHeight: Math.min(600, window.innerHeight - 50)
      }
    } else {
      return {
        minWidth: 400,
        minHeight: 300,
        maxWidth: window.innerWidth,
        maxHeight: window.innerHeight
      }
    }
  }

  const constraints = getConstraints()

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          style={{ zIndex: 1000 }}
        >
          <Rnd
            size={{ width: size.width, height: size.height }}
            position={{ x: position.x, y: position.y }}
            onDragStop={(e, d) => {
              if (!isMaximized) {
                setPosition({ x: d.x, y: d.y })
              }
            }}
            onResizeStop={(e, direction, ref, delta, position) => {
              if (!isMaximized) {
                setSize({
                  width: parseInt(ref.style.width),
                  height: parseInt(ref.style.height)
                })
                setPosition(position)
              }
            }}
            minWidth={constraints.minWidth}
            minHeight={constraints.minHeight}
            maxWidth={constraints.maxWidth}
            maxHeight={constraints.maxHeight}
            disableDragging={isMaximized}
            enableResizing={!isMaximized && resizable}
            dragHandleClassName="window-header"
            className={`rounded-2xl overflow-hidden shadow-2xl ${className}`}
            style={{
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 50%, rgba(255,255,255,0.12) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.32) 0%, rgba(139, 92, 246, 0.28) 25%, rgba(110, 140, 160, 0.26) 50%, rgba(90, 150, 140, 0.24) 75%, rgba(59, 130, 246, 0.22) 100%)
              `,
              border: '1px solid rgba(255,255,255,0.2)',
              backdropFilter: 'blur(20px) saturate(1.2) brightness(1.1)'
            }}
          >
            {/* Window Header */}
            <div
              className="window-header flex items-center justify-between p-4 cursor-move"
              style={{
                background: `
                  linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
                  linear-gradient(135deg, rgba(99, 102, 241, 0.20) 0%, rgba(139, 92, 246, 0.16) 25%, rgba(110, 140, 160, 0.14) 50%, rgba(90, 150, 140, 0.12) 75%, rgba(59, 130, 246, 0.10) 100%)
                `,
                borderBottom: '1px solid rgba(255,255,255,0.1)'
              }}
            >
              <h3 className="text-white font-semibold text-sm drop-shadow-lg">{title}</h3>
              
              <div className="flex items-center space-x-2">
                {onMinimize && (
                  <button
                    onClick={onMinimize}
                    className="w-6 h-6 rounded-full bg-yellow-500/80 hover:bg-yellow-500 flex items-center justify-center transition-colors"
                    title="Minimize"
                  >
                    <Minimize2 size={12} className="text-white" />
                  </button>
                )}
                
                <button
                  onClick={handleMaximize}
                  className="w-6 h-6 rounded-full bg-green-500/80 hover:bg-green-500 flex items-center justify-center transition-colors"
                  title={isMaximized ? "Restore" : "Maximize"}
                >
                  <Maximize2 size={12} className="text-white" />
                </button>
                
                <button
                  onClick={onClose}
                  className="w-6 h-6 rounded-full bg-red-500/80 hover:bg-red-500 flex items-center justify-center transition-colors"
                  title="Close"
                >
                  <X size={12} className="text-white" />
                </button>
              </div>
            </div>

            {/* Window Content */}
            <div className="flex-1 overflow-auto p-4" style={{ height: 'calc(100% - 64px)' }}>
              {children}
            </div>
          </Rnd>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
