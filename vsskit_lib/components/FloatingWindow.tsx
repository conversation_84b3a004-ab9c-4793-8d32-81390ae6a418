'use client'

import React, { useState } from 'react'
import { Rnd } from 'react-rnd'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Minimize2, Maximize2 } from 'lucide-react'

interface FloatingWindowProps {
  title: string
  isOpen: boolean
  onClose: () => void
  onMinimize?: () => void
  children: React.ReactNode
  defaultPosition?: { x: number; y: number }
  defaultSize?: { width: number; height: number }
  className?: string
  resizable?: boolean
  windowType?: 'panel' | 'app' // New prop to distinguish panel windows from app windows
}

export function FloatingWindow({
  title,
  isOpen,
  onClose,
  onMinimize,
  children,
  defaultPosition = { x: 100, y: 100 },
  defaultSize = { width: 400, height: 300 },
  className = '',
  resizable = true,
  windowType = 'app'
}: FloatingWindowProps) {
  const [isMaximized, setIsMaximized] = useState(false)
  const [position, setPosition] = useState(defaultPosition)
  const [size, setSize] = useState(defaultSize)
  const [originalSize, setOriginalSize] = useState(defaultSize)
  const [originalPosition, setOriginalPosition] = useState(defaultPosition)
  const [isResizing, setIsResizing] = useState(false)
  const [isDragging, setIsDragging] = useState(false)

  const handleMaximize = () => {
    if (isMaximized) {
      // Restore to original size and position
      setSize(originalSize)
      setPosition(originalPosition)
      setIsMaximized(false)
    } else {
      // Save current state before maximizing
      setOriginalSize(size)
      setOriginalPosition(position)

      if (windowType === 'panel') {
        // Panel windows: maximize to reasonable size, not full screen
        const maxWidth = Math.min(800, window.innerWidth - 100)
        const maxHeight = Math.min(600, window.innerHeight - 100)
        setSize({ width: maxWidth, height: maxHeight })
        setPosition({ x: 50, y: 50 })
      } else {
        // App windows: maximize to full screen (no header offset)
        setSize({ width: window.innerWidth, height: window.innerHeight })
        setPosition({ x: 0, y: 0 })
      }
      setIsMaximized(true)
    }
  }

  // Get constraints based on window type
  const getConstraints = () => {
    if (windowType === 'panel') {
      return {
        minWidth: 300,
        minHeight: 200,
        maxWidth: Math.min(800, window.innerWidth - 50),
        maxHeight: Math.min(600, window.innerHeight - 50)
      }
    } else {
      return {
        minWidth: 400,
        minHeight: 300,
        maxWidth: window.innerWidth,
        maxHeight: window.innerHeight
      }
    }
  }

  const constraints = getConstraints()

  // Neon Green Scaling Pills Component
  const ScalingPills = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[9999] pointer-events-none"
    >
      <div
        className="px-6 py-3 rounded-full text-white font-medium text-sm backdrop-blur-md"
        style={{
          background: `
            linear-gradient(135deg,
              rgba(0, 255, 127, 0.9) 0%,
              rgba(0, 255, 100, 0.8) 25%,
              rgba(50, 255, 130, 0.9) 50%,
              rgba(0, 255, 127, 0.8) 75%,
              rgba(20, 255, 120, 0.9) 100%
            )
          `,
          border: '2px solid rgba(0, 255, 127, 0.6)',
          boxShadow: `
            0 0 20px rgba(0, 255, 127, 0.4),
            0 0 40px rgba(0, 255, 127, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.3)
          `,
          textShadow: '0 1px 2px rgba(0, 0, 0, 0.5)'
        }}
      >
        {size.width} × {size.height}
      </div>
    </motion.div>
  )

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          style={{ zIndex: 1000 }}
        >
          <Rnd
            size={{ width: size.width, height: size.height }}
            position={{ x: position.x, y: position.y }}
            onDragStart={() => setIsDragging(true)}
            onDragStop={(e, d) => {
              setIsDragging(false)
              if (!isMaximized) {
                setPosition({ x: d.x, y: d.y })
              }
            }}
            onResizeStart={() => setIsResizing(true)}
            onResize={(e, direction, ref, delta, position) => {
              if (!isMaximized) {
                setSize({
                  width: parseInt(ref.style.width),
                  height: parseInt(ref.style.height)
                })
              }
            }}
            onResizeStop={(e, direction, ref, delta, position) => {
              setIsResizing(false)
              if (!isMaximized) {
                setSize({
                  width: parseInt(ref.style.width),
                  height: parseInt(ref.style.height)
                })
                setPosition(position)
              }
            }}
            minWidth={constraints.minWidth}
            minHeight={constraints.minHeight}
            maxWidth={constraints.maxWidth}
            maxHeight={constraints.maxHeight}
            disableDragging={isMaximized}
            enableResizing={!isMaximized && resizable}
            dragHandleClassName="window-header"
            className={`rounded-3xl overflow-hidden ${className}`}
            style={{
              // Apple 26 Flagship Window Design
              background: `
                linear-gradient(145deg,
                  rgba(255, 255, 255, 0.25) 0%,
                  rgba(255, 255, 255, 0.10) 25%,
                  rgba(255, 255, 255, 0.15) 50%,
                  rgba(255, 255, 255, 0.08) 75%,
                  rgba(255, 255, 255, 0.20) 100%
                ),
                linear-gradient(135deg,
                  rgba(99, 102, 241, 0.35) 0%,
                  rgba(139, 92, 246, 0.30) 20%,
                  rgba(110, 140, 160, 0.28) 40%,
                  rgba(90, 150, 140, 0.26) 60%,
                  rgba(59, 130, 246, 0.24) 80%,
                  rgba(99, 102, 241, 0.22) 100%
                )
              `,
              border: '1.5px solid rgba(255, 255, 255, 0.3)',
              borderTop: '2px solid rgba(255, 255, 255, 0.4)',
              borderLeft: '1.5px solid rgba(255, 255, 255, 0.35)',
              borderRight: '1px solid rgba(255, 255, 255, 0.25)',
              borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
              backdropFilter: 'blur(25px) saturate(1.4) brightness(1.15)',
              boxShadow: `
                0 25px 50px rgba(0, 0, 0, 0.25),
                0 15px 35px rgba(0, 0, 0, 0.15),
                0 5px 15px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1),
                inset 1px 0 0 rgba(255, 255, 255, 0.3),
                inset -1px 0 0 rgba(0, 0, 0, 0.05),
                0 0 0 1px rgba(255, 255, 255, 0.1)
              `,
              transform: isDragging ? 'scale(1.02)' : 'scale(1)',
              transition: 'transform 0.2s ease-out'
            }}
          >
            {/* Apple 26 Window Header */}
            <div
              className="window-header flex items-center justify-between px-6 py-4 cursor-move"
              style={{
                background: `
                  linear-gradient(145deg,
                    rgba(255, 255, 255, 0.20) 0%,
                    rgba(255, 255, 255, 0.08) 50%,
                    rgba(255, 255, 255, 0.15) 100%
                  ),
                  linear-gradient(135deg,
                    rgba(99, 102, 241, 0.25) 0%,
                    rgba(139, 92, 246, 0.20) 25%,
                    rgba(110, 140, 160, 0.18) 50%,
                    rgba(90, 150, 140, 0.16) 75%,
                    rgba(59, 130, 246, 0.14) 100%
                  )
                `,
                borderBottom: '1px solid rgba(255, 255, 255, 0.15)',
                borderRadius: '24px 24px 0 0'
              }}
            >
              <div className="flex items-center space-x-3">
                {/* Apple 26 Traffic Light Buttons */}
                <button
                  onClick={onClose}
                  className="w-3 h-3 rounded-full transition-all duration-200 group"
                  style={{
                    background: `
                      radial-gradient(circle at 30% 30%,
                        rgba(255, 95, 87, 1) 0%,
                        rgba(255, 69, 58, 0.9) 70%,
                        rgba(255, 45, 32, 0.8) 100%
                      )
                    `,
                    border: '0.5px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: `
                      0 1px 3px rgba(0, 0, 0, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.4)
                    `
                  }}
                  title="Close"
                >
                  <X size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                </button>

                {onMinimize && (
                  <button
                    onClick={onMinimize}
                    className="w-3 h-3 rounded-full transition-all duration-200 group"
                    style={{
                      background: `
                        radial-gradient(circle at 30% 30%,
                          rgba(255, 204, 0, 1) 0%,
                          rgba(255, 193, 7, 0.9) 70%,
                          rgba(255, 179, 0, 0.8) 100%
                        )
                      `,
                      border: '0.5px solid rgba(255, 255, 255, 0.3)',
                      boxShadow: `
                        0 1px 3px rgba(0, 0, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.4)
                      `
                    }}
                    title="Minimize"
                  >
                    <Minimize2 size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                  </button>
                )}

                <button
                  onClick={handleMaximize}
                  className="w-3 h-3 rounded-full transition-all duration-200 group"
                  style={{
                    background: `
                      radial-gradient(circle at 30% 30%,
                        rgba(40, 205, 65, 1) 0%,
                        rgba(52, 199, 89, 0.9) 70%,
                        rgba(48, 176, 199, 0.8) 100%
                      )
                    `,
                    border: '0.5px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: `
                      0 1px 3px rgba(0, 0, 0, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.4)
                    `
                  }}
                  title={isMaximized ? "Restore" : "Maximize"}
                >
                  <Maximize2 size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                </button>
              </div>

              <h3 className="text-white font-medium text-sm drop-shadow-lg tracking-wide">{title}</h3>

              <div className="w-12"></div> {/* Spacer for centering */}
            </div>

            {/* Apple 26 Window Content */}
            <div
              className="flex-1 overflow-auto p-6"
              style={{
                height: 'calc(100% - 80px)',
                background: `
                  linear-gradient(145deg,
                    rgba(255, 255, 255, 0.08) 0%,
                    rgba(255, 255, 255, 0.03) 50%,
                    rgba(255, 255, 255, 0.06) 100%
                  )
                `,
                borderRadius: '0 0 24px 24px'
              }}
            >
              {children}
            </div>
          </Rnd>

          {/* Neon Green Scaling Pills - Show during resize */}
          <AnimatePresence>
            {isResizing && <ScalingPills />}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
