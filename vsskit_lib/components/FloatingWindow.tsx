'use client'

import React, { useState } from 'react'
import { Rnd } from 'react-rnd'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Minimize2, Maximize2 } from 'lucide-react'

interface FloatingWindowProps {
  title: string
  isOpen: boolean
  onClose: () => void
  onMinimize?: () => void
  children: React.ReactNode
  defaultPosition?: { x: number; y: number }
  defaultSize?: { width: number; height: number }
  className?: string
  resizable?: boolean
  windowType?: 'panel' | 'app' // New prop to distinguish panel windows from app windows
}

export function FloatingWindow({
  title,
  isOpen,
  onClose,
  onMinimize,
  children,
  defaultPosition = { x: 100, y: 100 },
  defaultSize = { width: 400, height: 300 },
  className = '',
  resizable = true,
  windowType = 'app'
}: FloatingWindowProps) {
  const [isMaximized, setIsMaximized] = useState(false)
  const [position, setPosition] = useState(defaultPosition)
  const [size, setSize] = useState(defaultSize)
  const [originalSize, setOriginalSize] = useState(defaultSize)
  const [originalPosition, setOriginalPosition] = useState(defaultPosition)
  const [isResizing, setIsResizing] = useState(false)
  const [isDragging, setIsDragging] = useState(false)

  const handleMaximize = () => {
    if (isMaximized) {
      // Restore to original size and position
      setSize(originalSize)
      setPosition(originalPosition)
      setIsMaximized(false)
    } else {
      // Save current state before maximizing
      setOriginalSize(size)
      setOriginalPosition(position)

      if (windowType === 'panel') {
        // Panel windows: maximize to reasonable size, not full screen
        const maxWidth = Math.min(800, window.innerWidth - 100)
        const maxHeight = Math.min(600, window.innerHeight - 100)
        setSize({ width: maxWidth, height: maxHeight })
        setPosition({ x: 50, y: 50 })
      } else {
        // App windows: maximize to full screen (no header offset)
        setSize({ width: window.innerWidth, height: window.innerHeight })
        setPosition({ x: 0, y: 0 })
      }
      setIsMaximized(true)
    }
  }

  // Get constraints based on window type
  const getConstraints = () => {
    if (windowType === 'panel') {
      return {
        minWidth: 300,
        minHeight: 200,
        maxWidth: Math.min(800, window.innerWidth - 50),
        maxHeight: Math.min(600, window.innerHeight - 50)
      }
    } else {
      return {
        minWidth: 400,
        minHeight: 300,
        maxWidth: window.innerWidth,
        maxHeight: window.innerHeight
      }
    }
  }

  const constraints = getConstraints()

  // Apple 26 Scale Indicators Component - Fixed Overlay
  const Apple26ScaleIndicators = () => (
    <div
      className="pointer-events-none fixed"
      style={{
        left: 0,
        top: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 10000 // Above everything
      }}
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="absolute"
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${size.width}px`,
          height: `${size.height}px`
        }}
      >
      {/* Selection Pill Style - Bottom-Right Corner */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.1 }}
        className="absolute rounded-full"
        style={{
          bottom: '-8px',
          right: '-8px',
          width: '24px',
          height: '4px',
          borderRadius: '2px',
          background: `
            linear-gradient(135deg,
              rgba(0, 255, 127, 0.9) 0%,
              rgba(0, 255, 127, 1) 30%,
              rgba(0, 255, 127, 1) 70%,
              rgba(0, 255, 127, 0.9) 100%
            ),
            linear-gradient(90deg,
              rgba(255, 255, 255, 0.2) 0%,
              rgba(255, 255, 255, 0.1) 50%,
              rgba(255, 255, 255, 0.2) 100%
            )
          `,
          border: '1px solid rgba(255, 255, 255, 0.3)',
          boxShadow: `
            0 0 12px rgba(0, 255, 127, 0.6),
            0 0 24px rgba(0, 255, 127, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4)
          `,
          backdropFilter: 'blur(8px)'
        }}
      />

      {/* Selection Pill Style - Right Edge */}
      <motion.div
        initial={{ scaleY: 0, opacity: 0 }}
        animate={{ scaleY: 1, opacity: 1 }}
        transition={{ delay: 0.05 }}
        className="absolute rounded-full"
        style={{
          top: '50%',
          right: '-8px',
          width: '4px',
          height: '24px',
          borderRadius: '2px',
          transform: 'translateY(-50%)',
          background: `
            linear-gradient(135deg,
              rgba(0, 255, 127, 0.9) 0%,
              rgba(0, 255, 127, 1) 30%,
              rgba(0, 255, 127, 1) 70%,
              rgba(0, 255, 127, 0.9) 100%
            ),
            linear-gradient(90deg,
              rgba(255, 255, 255, 0.2) 0%,
              rgba(255, 255, 255, 0.1) 50%,
              rgba(255, 255, 255, 0.2) 100%
            )
          `,
          border: '1px solid rgba(255, 255, 255, 0.3)',
          boxShadow: `
            0 0 12px rgba(0, 255, 127, 0.6),
            0 0 24px rgba(0, 255, 127, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4)
          `,
          backdropFilter: 'blur(8px)'
        }}
      />

      {/* Selection Pill Style - Bottom Edge */}
      <motion.div
        initial={{ scaleX: 0, opacity: 0 }}
        animate={{ scaleX: 1, opacity: 1 }}
        transition={{ delay: 0.05 }}
        className="absolute rounded-full"
        style={{
          bottom: '-8px',
          left: '50%',
          width: '24px',
          height: '4px',
          borderRadius: '2px',
          transform: 'translateX(-50%)',
          background: `
            linear-gradient(135deg,
              rgba(0, 255, 127, 0.9) 0%,
              rgba(0, 255, 127, 1) 30%,
              rgba(0, 255, 127, 1) 70%,
              rgba(0, 255, 127, 0.9) 100%
            ),
            linear-gradient(90deg,
              rgba(255, 255, 255, 0.2) 0%,
              rgba(255, 255, 255, 0.1) 50%,
              rgba(255, 255, 255, 0.2) 100%
            )
          `,
          border: '1px solid rgba(255, 255, 255, 0.3)',
          boxShadow: `
            0 0 12px rgba(0, 255, 127, 0.6),
            0 0 24px rgba(0, 255, 127, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.4)
          `,
          backdropFilter: 'blur(8px)'
        }}
      />

      {/* Minimal Corner Dots */}
      {/* Top-Left Corner Dot */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.15 }}
        className="absolute rounded-full"
        style={{
          top: '-1px',
          left: '-1px',
          width: '2px',
          height: '2px',
          background: 'rgba(0, 255, 127, 0.8)',
          boxShadow: `
            0 0 4px rgba(0, 255, 127, 0.6),
            0 0 8px rgba(0, 255, 127, 0.3)
          `
        }}
      />

      {/* Top-Right Corner Dot */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.15 }}
        className="absolute rounded-full"
        style={{
          top: '-1px',
          right: '-1px',
          width: '2px',
          height: '2px',
          background: 'rgba(0, 255, 127, 0.8)',
          boxShadow: `
            0 0 4px rgba(0, 255, 127, 0.6),
            0 0 8px rgba(0, 255, 127, 0.3)
          `
        }}
      />

      {/* Bottom-Left Corner Dot */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: 0.15 }}
        className="absolute rounded-full"
        style={{
          bottom: '-1px',
          left: '-1px',
          width: '2px',
          height: '2px',
          background: 'rgba(0, 255, 127, 0.8)',
          boxShadow: `
            0 0 4px rgba(0, 255, 127, 0.6),
            0 0 8px rgba(0, 255, 127, 0.3)
          `
        }}
      />

      {/* Minimal Size Display */}
      <motion.div
        initial={{ opacity: 0, y: -4, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ delay: 0.1 }}
        className="absolute px-2 py-1 rounded-md text-white text-xs font-light backdrop-blur-sm"
        style={{
          top: '-28px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'rgba(0, 0, 0, 0.7)',
          border: '1px solid rgba(255, 255, 255, 0.15)',
          boxShadow: `
            0 2px 8px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1)
          `
        }}
      >
        {size.width} × {size.height}
      </motion.div>
    </div>
  )

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          style={{ zIndex: 1000 }}
        >
          <Rnd
            size={{ width: size.width, height: size.height }}
            position={{ x: position.x, y: position.y }}
            onDragStart={() => setIsDragging(true)}
            onDragStop={(e, d) => {
              setIsDragging(false)
              if (!isMaximized) {
                setPosition({ x: d.x, y: d.y })
              }
            }}
            onResizeStart={() => setIsResizing(true)}
            onResize={(e, direction, ref, delta, position) => {
              if (!isMaximized) {
                setSize({
                  width: parseInt(ref.style.width),
                  height: parseInt(ref.style.height)
                })
              }
            }}
            onResizeStop={(e, direction, ref, delta, position) => {
              setIsResizing(false)
              if (!isMaximized) {
                setSize({
                  width: parseInt(ref.style.width),
                  height: parseInt(ref.style.height)
                })
                setPosition(position)
              }
            }}
            minWidth={constraints.minWidth}
            minHeight={constraints.minHeight}
            maxWidth={constraints.maxWidth}
            maxHeight={constraints.maxHeight}
            disableDragging={isMaximized}
            enableResizing={!isMaximized && resizable}
            dragHandleClassName="window-header"
            className={`rounded-3xl overflow-hidden ${className}`}
            style={{
              // Original DesktopWidgets Material Texture - Maintain Consistency
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.03) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.18) 0%, rgba(139, 92, 246, 0.16) 25%, rgba(110, 140, 160, 0.14) 50%, rgba(90, 150, 140, 0.12) 75%, rgba(59, 130, 246, 0.10) 100%)
              `,
              border: '1px solid rgba(255,255,255,0.2)',
              backdropFilter: 'blur(20px) saturate(1.2) brightness(1.1)',
              boxShadow: `
                0 8px 32px rgba(0,0,0,0.2),
                0 4px 16px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.3),
                inset 0 -1px 0 rgba(0,0,0,0.1),
                inset 1px 0 0 rgba(255,255,255,0.2),
                inset -1px 0 0 rgba(0,0,0,0.05)
              `,
              transform: isDragging ? 'scale(1.02)' : 'scale(1)',
              transition: 'transform 0.2s ease-out'
            }}
          >
            {/* Apple 26 Window Header */}
            <div
              className="window-header flex items-center justify-between px-6 py-4 cursor-move"
              style={{
                background: `
                  linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
                  linear-gradient(135deg, rgba(99, 102, 241, 0.20) 0%, rgba(139, 92, 246, 0.16) 25%, rgba(110, 140, 160, 0.14) 50%, rgba(90, 150, 140, 0.12) 75%, rgba(59, 130, 246, 0.10) 100%)
                `,
                borderBottom: '1px solid rgba(255,255,255,0.1)',
                borderRadius: '24px 24px 0 0'
              }}
            >
                <h3 className="text-white font-medium text-sm drop-shadow-lg tracking-wide">{title}</h3>

              <div className="flex items-center space-x-3">
                {/* Apple 26 Traffic Light Buttons - Moved to Right */}
                <button
                  onClick={onClose}
                  className="w-3 h-3 rounded-full transition-all duration-200 group"
                  style={{
                    background: `
                      radial-gradient(circle at 30% 30%,
                        rgba(255, 95, 87, 1) 0%,
                        rgba(255, 69, 58, 0.9) 70%,
                        rgba(255, 45, 32, 0.8) 100%
                      )
                    `,
                    border: '0.5px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: `
                      0 1px 3px rgba(0, 0, 0, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.4)
                    `
                  }}
                  title="Close"
                >
                  <X size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                </button>

                {onMinimize && (
                  <button
                    onClick={onMinimize}
                    className="w-3 h-3 rounded-full transition-all duration-200 group"
                    style={{
                      background: `
                        radial-gradient(circle at 30% 30%,
                          rgba(255, 204, 0, 1) 0%,
                          rgba(255, 193, 7, 0.9) 70%,
                          rgba(255, 179, 0, 0.8) 100%
                        )
                      `,
                      border: '0.5px solid rgba(255, 255, 255, 0.3)',
                      boxShadow: `
                        0 1px 3px rgba(0, 0, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.4)
                      `
                    }}
                    title="Minimize"
                  >
                    <Minimize2 size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                  </button>
                )}

                <button
                  onClick={handleMaximize}
                  className="w-3 h-3 rounded-full transition-all duration-200 group"
                  style={{
                    background: `
                      radial-gradient(circle at 30% 30%,
                        rgba(40, 205, 65, 1) 0%,
                        rgba(52, 199, 89, 0.9) 70%,
                        rgba(48, 176, 199, 0.8) 100%
                      )
                    `,
                    border: '0.5px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: `
                      0 1px 3px rgba(0, 0, 0, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.4)
                    `
                  }}
                  title={isMaximized ? "Restore" : "Maximize"}
                >
                  <Maximize2 size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                </button>
              </div>
            </div>

            {/* Apple 26 Window Content */}
            <div
              className="flex-1 overflow-auto p-6"
              style={{
                height: 'calc(100% - 80px)',
                background: `
                  linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)
                `,
                borderRadius: '0 0 24px 24px'
              }}
            >
              {children}
            </div>
          </Rnd>

        {/* Apple 26 Scale Indicators - Render outside window */}
        {isResizing && (
          <Apple26ScaleIndicators />
        )}
        </motion.div>
      )}
    </AnimatePresence>
  )
}
