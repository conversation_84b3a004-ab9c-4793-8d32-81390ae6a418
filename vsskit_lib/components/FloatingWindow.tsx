'use client'

import React, { useState } from 'react'
import { Rnd } from 'react-rnd'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Minimize2, Maximize2 } from 'lucide-react'

interface FloatingWindowProps {
  title: string
  isOpen: boolean
  onClose: () => void
  onMinimize?: () => void
  children: React.ReactNode
  defaultPosition?: { x: number; y: number }
  defaultSize?: { width: number; height: number }
  className?: string
  resizable?: boolean
  windowType?: 'panel' | 'app' // New prop to distinguish panel windows from app windows
}

export function FloatingWindow({
  title,
  isOpen,
  onClose,
  onMinimize,
  children,
  defaultPosition = { x: 100, y: 100 },
  defaultSize = { width: 400, height: 300 },
  className = '',
  resizable = true,
  windowType = 'app'
}: FloatingWindowProps) {
  const [isMaximized, setIsMaximized] = useState(false)
  const [position, setPosition] = useState(defaultPosition)
  const [size, setSize] = useState(defaultSize)
  const [originalSize, setOriginalSize] = useState(defaultSize)
  const [originalPosition, setOriginalPosition] = useState(defaultPosition)
  const [isResizing, setIsResizing] = useState(false)
  const [isDragging, setIsDragging] = useState(false)

  const handleMaximize = () => {
    if (isMaximized) {
      // Restore to original size and position
      setSize(originalSize)
      setPosition(originalPosition)
      setIsMaximized(false)
    } else {
      // Save current state before maximizing
      setOriginalSize(size)
      setOriginalPosition(position)

      if (windowType === 'panel') {
        // Panel windows: maximize to reasonable size, not full screen
        const maxWidth = Math.min(800, window.innerWidth - 100)
        const maxHeight = Math.min(600, window.innerHeight - 100)
        setSize({ width: maxWidth, height: maxHeight })
        setPosition({ x: 50, y: 50 })
      } else {
        // App windows: maximize to full screen (no header offset)
        setSize({ width: window.innerWidth, height: window.innerHeight })
        setPosition({ x: 0, y: 0 })
      }
      setIsMaximized(true)
    }
  }

  // Get constraints based on window type
  const getConstraints = () => {
    if (windowType === 'panel') {
      return {
        minWidth: 300,
        minHeight: 200,
        maxWidth: Math.min(800, window.innerWidth - 50),
        maxHeight: Math.min(600, window.innerHeight - 50)
      }
    } else {
      return {
        minWidth: 400,
        minHeight: 300,
        maxWidth: window.innerWidth,
        maxHeight: window.innerHeight
      }
    }
  }

  const constraints = getConstraints()

  // Complete set of resize handles - visible only during resize
  const getResizeHandleStyles = () => {
    const baseHandleStyle = {
      background: `
        linear-gradient(135deg,
          rgba(0, 255, 127, 0.9) 0%,
          rgba(0, 255, 127, 1) 30%,
          rgba(0, 255, 127, 1) 70%,
          rgba(0, 255, 127, 0.9) 100%
        ),
        linear-gradient(90deg,
          rgba(255, 255, 255, 0.2) 0%,
          rgba(255, 255, 255, 0.1) 50%,
          rgba(255, 255, 255, 0.2) 100%
        )
      `,
      border: '1px solid rgba(255, 255, 255, 0.3)',
      borderRadius: '2px',
      boxShadow: `
        0 0 8px rgba(0, 255, 127, 0.6),
        0 0 16px rgba(0, 255, 127, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4)
      `,
      backdropFilter: 'blur(4px)'
    }

    return {
      // Edge handles
      top: {
        ...baseHandleStyle,
        height: '4px',
        width: '24px',
        left: '50%',
        transform: 'translateX(-50%)',
        top: '-2px'
      },
      bottom: {
        ...baseHandleStyle,
        height: '4px',
        width: '24px',
        left: '50%',
        transform: 'translateX(-50%)',
        bottom: '-2px'
      },
      left: {
        ...baseHandleStyle,
        width: '4px',
        height: '24px',
        top: '50%',
        transform: 'translateY(-50%)',
        left: '-2px'
      },
      right: {
        ...baseHandleStyle,
        width: '4px',
        height: '24px',
        top: '50%',
        transform: 'translateY(-50%)',
        right: '-2px'
      },
      // Corner handles
      topLeft: {
        ...baseHandleStyle,
        width: '8px',
        height: '8px',
        top: '-2px',
        left: '-2px'
      },
      topRight: {
        ...baseHandleStyle,
        width: '8px',
        height: '8px',
        top: '-2px',
        right: '-2px'
      },
      bottomLeft: {
        ...baseHandleStyle,
        width: '8px',
        height: '8px',
        bottom: '-2px',
        left: '-2px'
      },
      bottomRight: {
        ...baseHandleStyle,
        width: '8px',
        height: '8px',
        bottom: '-2px',
        right: '-2px'
      }
    }
  }

  // Size display component
  const SizeDisplay = () => (
    <motion.div
      initial={{ opacity: 0, y: -4, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ delay: 0.1 }}
      className="fixed px-2 py-1 rounded-md text-white text-xs font-light backdrop-blur-sm pointer-events-none"
      style={{
        top: `${position.y - 35}px`,
        left: `${position.x + size.width / 2}px`,
        transform: 'translateX(-50%)',
        background: 'rgba(0, 0, 0, 0.7)',
        border: '1px solid rgba(255, 255, 255, 0.15)',
        boxShadow: `
          0 2px 8px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.1)
        `,
        zIndex: 10000
      }}
    >
      {size.width} × {size.height}
    </motion.div>
  )

  return (
    <AnimatePresence>
      {isOpen && (
        <Rnd
            size={{ width: size.width, height: size.height }}
            position={{ x: position.x, y: position.y }}
            onDragStart={() => {
              console.log('Drag started')
              setIsDragging(true)
            }}
            onDragStop={(e, d) => {
              console.log('Drag stopped', d)
              setIsDragging(false)
              if (!isMaximized) {
                setPosition({ x: d.x, y: d.y })
              }
            }}
            onResizeStart={() => {
              console.log('Resize started')
              setIsResizing(true)
            }}
            onResize={(e, direction, ref, delta, position) => {
              console.log('Resizing', ref.style.width, ref.style.height)
              if (!isMaximized) {
                setSize({
                  width: parseInt(ref.style.width),
                  height: parseInt(ref.style.height)
                })
              }
            }}
            onResizeStop={(e, direction, ref, delta, position) => {
              console.log('Resize stopped')
              setIsResizing(false)
              if (!isMaximized) {
                setSize({
                  width: parseInt(ref.style.width),
                  height: parseInt(ref.style.height)
                })
                setPosition(position)
              }
            }}
            minWidth={constraints.minWidth}
            minHeight={constraints.minHeight}
            maxWidth={constraints.maxWidth}
            maxHeight={constraints.maxHeight}
            disableDragging={isMaximized}
            enableResizing={!isMaximized && resizable ? {
              top: true,
              right: true,
              bottom: true,
              left: true,
              topRight: true,
              bottomRight: true,
              bottomLeft: true,
              topLeft: true
            } : false}
            resizeHandleStyles={!isMaximized && resizable && isResizing ? getResizeHandleStyles() : {}}
            dragHandleClassName="window-header"
            className={`rounded-3xl overflow-hidden ${className}`}
            style={{
              // Original DesktopWidgets Material Texture - Maintain Consistency
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.03) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.18) 0%, rgba(139, 92, 246, 0.16) 25%, rgba(110, 140, 160, 0.14) 50%, rgba(90, 150, 140, 0.12) 75%, rgba(59, 130, 246, 0.10) 100%)
              `,
              border: '1px solid rgba(255,255,255,0.2)',
              backdropFilter: 'blur(20px) saturate(1.2) brightness(1.1)',
              boxShadow: `
                0 8px 32px rgba(0,0,0,0.2),
                0 4px 16px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.3),
                inset 0 -1px 0 rgba(0,0,0,0.1),
                inset 1px 0 0 rgba(255,255,255,0.2),
                inset -1px 0 0 rgba(0,0,0,0.05)
              `,
              transform: isDragging ? 'scale(1.02)' : 'scale(1)',
              transition: 'transform 0.2s ease-out',
              zIndex: 1000
            }}
          >
            {/* Apple 26 Window Header */}
            <div
              className="window-header flex items-center justify-between px-6 py-4 cursor-move"
              style={{
                background: `
                  linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
                  linear-gradient(135deg, rgba(99, 102, 241, 0.20) 0%, rgba(139, 92, 246, 0.16) 25%, rgba(110, 140, 160, 0.14) 50%, rgba(90, 150, 140, 0.12) 75%, rgba(59, 130, 246, 0.10) 100%)
                `,
                borderBottom: '1px solid rgba(255,255,255,0.1)',
                borderRadius: '24px 24px 0 0'
              }}
            >
                <h3 className="text-white font-medium text-sm drop-shadow-lg tracking-wide">{title}</h3>

              <div className="flex items-center space-x-3">
                {/* Windows-Style Control Buttons: Minimize, Maximize, Close */}
                {onMinimize && (
                  <button
                    onClick={onMinimize}
                    className="w-3 h-3 rounded-full transition-all duration-200 group"
                    style={{
                      background: `
                        radial-gradient(circle at 30% 30%,
                          rgba(255, 204, 0, 1) 0%,
                          rgba(255, 193, 7, 0.9) 70%,
                          rgba(255, 179, 0, 0.8) 100%
                        )
                      `,
                      border: '0.5px solid rgba(255, 255, 255, 0.3)',
                      boxShadow: `
                        0 1px 3px rgba(0, 0, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.4)
                      `
                    }}
                    title="Minimize"
                  >
                    <Minimize2 size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                  </button>
                )}

                <button
                  onClick={handleMaximize}
                  className="w-3 h-3 rounded-full transition-all duration-200 group"
                  style={{
                    background: `
                      radial-gradient(circle at 30% 30%,
                        rgba(40, 205, 65, 1) 0%,
                        rgba(52, 199, 89, 0.9) 70%,
                        rgba(48, 176, 199, 0.8) 100%
                      )
                    `,
                    border: '0.5px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: `
                      0 1px 3px rgba(0, 0, 0, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.4)
                    `
                  }}
                  title={isMaximized ? "Restore" : "Maximize"}
                >
                  <Maximize2 size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                </button>

                <button
                  onClick={onClose}
                  className="w-3 h-3 rounded-full transition-all duration-200 group"
                  style={{
                    background: `
                      radial-gradient(circle at 30% 30%,
                        rgba(255, 95, 87, 1) 0%,
                        rgba(255, 69, 58, 0.9) 70%,
                        rgba(255, 45, 32, 0.8) 100%
                      )
                    `,
                    border: '0.5px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: `
                      0 1px 3px rgba(0, 0, 0, 0.2),
                      inset 0 1px 0 rgba(255, 255, 255, 0.4)
                    `
                  }}
                  title="Close"
                >
                  <X size={8} className="text-white/80 opacity-0 group-hover:opacity-100 transition-opacity mx-auto" />
                </button>
              </div>
            </div>

            {/* Apple 26 Window Content */}
            <div
              className="flex-1 overflow-auto p-6"
              style={{
                height: 'calc(100% - 80px)',
                background: `
                  linear-gradient(135deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%)
                `,
                borderRadius: '0 0 24px 24px'
              }}
            >
              {children}
            </div>
          </Rnd>
      )}

      {/* Professional Size Display - Show during resize */}
      <AnimatePresence>
        {isOpen && isResizing && <SizeDisplay />}
      </AnimatePresence>
    </AnimatePresence>
  )
}
