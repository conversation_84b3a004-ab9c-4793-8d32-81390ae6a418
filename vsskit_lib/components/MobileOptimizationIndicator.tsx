'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useMobileOptimization } from '@/lib/mobile-optimization'

export function MobileOptimizationIndicator() {
  const { capabilities, settings } = useMobileOptimization()
  const [showIndicator, setShowIndicator] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  // Show indicator for 3 seconds when capabilities change
  useEffect(() => {
    if (capabilities) {
      setShowIndicator(true)
      const timer = setTimeout(() => setShowIndicator(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [capabilities])

  if (!capabilities || !settings) return null

  const getPerformanceColor = () => {
    switch (capabilities.performanceLevel) {
      case 'high': return 'from-green-500 to-emerald-500'
      case 'medium': return 'from-yellow-500 to-orange-500'
      case 'low': return 'from-red-500 to-pink-500'
      default: return 'from-gray-500 to-gray-600'
    }
  }

  const getDeviceIcon = () => {
    if (capabilities.isMobile) return '📱'
    if (capabilities.isTablet) return '📟'
    return '💻'
  }

  return (
    <AnimatePresence>
      {showIndicator && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          className="fixed top-4 right-4 z-[9999] pointer-events-auto"
        >
          <div
            className="backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden cursor-pointer"
            onClick={() => setShowDetails(!showDetails)}
            style={{
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
                linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.05) 100%)
              `,
              boxShadow: `
                0 8px 32px rgba(0,0,0,0.3),
                0 4px 16px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.3),
                inset 0 -1px 0 rgba(0,0,0,0.1)
              `
            }}
          >
            {/* Compact View */}
            <div className="p-3 flex items-center gap-3">
              <div className="text-2xl">{getDeviceIcon()}</div>
              <div className="flex-1 min-w-0">
                <div className="text-white text-sm font-medium">
                  {capabilities.isMobile ? 'Mobile' : capabilities.isTablet ? 'Tablet' : 'Desktop'} Mode
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className={`h-2 w-16 rounded-full bg-gradient-to-r ${getPerformanceColor()}`}
                    style={{
                      boxShadow: '0 2px 4px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.3)'
                    }}
                  />
                  <span className="text-white/70 text-xs capitalize">
                    {capabilities.performanceLevel}
                  </span>
                </div>
              </div>
              <div className="text-white/60 text-xs">
                {showDetails ? '▼' : '▶'}
              </div>
            </div>

            {/* Detailed View */}
            <AnimatePresence>
              {showDetails && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="border-t border-white/20 overflow-hidden"
                  style={{
                    background: 'linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.1) 100%)'
                  }}
                >
                  <div className="p-3 space-y-2">
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="text-white/70">Screen:</div>
                      <div className="text-white capitalize">{capabilities.screenSize}</div>
                      
                      <div className="text-white/70">Touch:</div>
                      <div className="text-white">{capabilities.isTouch ? 'Yes' : 'No'}</div>
                      
                      <div className="text-white/70">Memory:</div>
                      <div className="text-white">{capabilities.deviceMemory}GB</div>
                      
                      <div className="text-white/70">CPU Cores:</div>
                      <div className="text-white">{capabilities.hardwareConcurrency}</div>
                    </div>

                    <div className="border-t border-white/10 pt-2">
                      <div className="text-white/70 text-xs mb-1">Optimizations:</div>
                      <div className="flex flex-wrap gap-1">
                        {settings.enableAnimations && (
                          <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded">
                            Animations
                          </span>
                        )}
                        {settings.enableBlur && (
                          <span className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded">
                            Blur
                          </span>
                        )}
                        {settings.enableShadows && (
                          <span className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded">
                            Shadows
                          </span>
                        )}
                        {!settings.enableAnimations && (
                          <span className="px-2 py-1 bg-red-500/20 text-red-300 text-xs rounded">
                            Reduced Effects
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="border-t border-white/10 pt-2">
                      <div className="text-white/70 text-xs mb-1">Touch Targets:</div>
                      <div className="text-white text-xs">{settings.touchTargetSize}px minimum</div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Hook for components to check if they should show mobile optimizations
export function useMobileIndicator() {
  const { capabilities } = useMobileOptimization()
  
  return {
    shouldShowMobileIndicator: capabilities?.isMobile || capabilities?.isTouch || false,
    deviceType: capabilities?.isMobile ? 'mobile' : capabilities?.isTablet ? 'tablet' : 'desktop',
    performanceLevel: capabilities?.performanceLevel || 'medium'
  }
}
