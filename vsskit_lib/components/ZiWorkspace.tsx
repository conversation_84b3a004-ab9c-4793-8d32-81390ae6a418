'use client'

import { useEffect, useState } from 'react'
import { useAppStore } from '@/lib/stores/app-store'
import { AppleDesktopEnvironment } from '@/components/AppleDesktopEnvironment'
import { HarmonyTaskbar } from '@/components/HarmonyTaskbar'
import { DesktopWidgets } from '@/components/DesktopWidgets'
import { LocalAIAssistant } from '@/components/LocalAIAssistant'
import { MobileOptimizationIndicator } from '@/components/MobileOptimizationIndicator'
import { liquidGlass } from '@/lib/liquid-glass'
import { initializeLiquidGlass, logLiquidGlassStatus, setupLiquidGlassForDev } from '@/lib/liquid-glass-utils'
import { useMobileOptimization } from '@/lib/mobile-optimization'

export function ZiWorkspace() {
  const { currentView, isDockOpen } = useAppStore()
  const [isInitialized, setIsInitialized] = useState(false)
  const [wallpaper, setWallpaper] = useState('/Enhanced/Images/wallpaper.jpg')
  const [showZiAssist, setShowZiAssist] = useState(false)
  const [ziAssistMode, setZiAssistMode] = useState<'sidebar' | 'window'>('sidebar')

  // Mobile optimization hook
  const { capabilities, settings } = useMobileOptimization()
  const isMobile = capabilities?.isMobile || false
  const isLowEndDevice = capabilities?.isLowEnd || false

  // Log device capabilities for debugging
  useEffect(() => {
    if (capabilities) {
      console.log('🔧 ZiWorkspace Device Capabilities:', {
        isMobile,
        isLowEndDevice,
        screenSize: capabilities.screenSize,
        performanceLevel: capabilities.performanceLevel,
        supportsAdvancedEffects: capabilities.supportsAdvancedEffects,
        settings
      })
    }
  }, [capabilities, settings, isMobile, isLowEndDevice])

  // Initialize workspace when entering ziworkspace mode
  useEffect(() => {
    if (currentView === 'ziworkspace') {
      setIsInitialized(true)
      // Initialize virtual file system (only if not low-end device)
      if (!isLowEndDevice) {
        initializeVirtualFileSystem()
      }
      // Initialize liquid glass system with performance considerations
      initializeLiquidGlassSystem()
    } else {
      setIsInitialized(false)
    }
  }, [currentView, isLowEndDevice])

  const initializeLiquidGlassSystem = async () => {
    try {
      // Initialize the liquid glass system
      initializeLiquidGlass()

      // Setup dev tools in development
      if (process.env.NODE_ENV === 'development') {
        setupLiquidGlassForDev()
        // Log status after a short delay to ensure everything is loaded
        setTimeout(() => {
          logLiquidGlassStatus()
        }, 1000)
      }

      // Force initialization of liquid glass manager
      const config = liquidGlass.getConfig()
      const metrics = liquidGlass.getPerformanceMetrics()
      console.log('🔮 Liquid Glass initialized:', {
        enableAdvancedEffects: config.enableAdvancedEffects,
        isLowEnd: metrics.isLowEnd,
        adaptiveEnabled: config.adaptive.enableColorAdaptation
      })
    } catch (error) {
      console.error('Failed to initialize liquid glass:', error)
    }
  }

  const initializeVirtualFileSystem = async () => {
    // Initialize BrowserFS and virtual file system
    try {
      const { configure, BFSRequire } = await import('browserfs')
      configure({
        fs: "MountableFileSystem",
        options: {
          "/": { fs: "IndexedDB", options: {} },
          "/tmp": { fs: "InMemory", options: {} }
        }
      }, (e) => {
        if (e) {
          console.error('Failed to initialize BrowserFS:', e)
        } else {
          console.log('BrowserFS initialized successfully')
        }
      })
    } catch (error) {
      console.error('Error initializing virtual file system:', error)
    }
  }

  if (currentView !== 'ziworkspace') {
    return null
  }

  return (
    <div
      id="ziworkspace"
      className={`fixed inset-0 top-10 z-10 overflow-hidden ${isMobile ? 'mobile-workspace' : 'desktop-workspace'} ${isLowEndDevice ? 'low-end-device' : ''}`}
      style={{
        // Remove background to allow wallpaper to show through
        backgroundColor: 'transparent'
      }}
    >
      {/* Apple 26 Desktop Environment with Custom Icons */}
      <AppleDesktopEnvironment
        wallpaper={wallpaper}
        onWallpaperChange={setWallpaper}
        isInitialized={isInitialized}
        isMobile={isMobile}
        isLowEndDevice={isLowEndDevice}
      />

      {/* Desktop Widgets - Optimized for mobile and low-end devices */}
      {(!isMobile || !isLowEndDevice) && (
        <DesktopWidgets
          isInitialized={isInitialized}
          isVisible={!(showZiAssist && ziAssistMode === 'sidebar')}
          isMobile={isMobile}
          isLowEndDevice={isLowEndDevice}
        />
      )}

      {/* Harmony OS Style Taskbar with ZiAssist - Mobile optimized */}
      <HarmonyTaskbar
        showZiAssist={showZiAssist}
        onZiAssistToggle={() => setShowZiAssist(!showZiAssist)}
        isMobile={isMobile}
        isLowEndDevice={isLowEndDevice}
      />

      {/* ZiAssist AI Assistant - Mobile optimized */}
      <LocalAIAssistant
        isOpen={showZiAssist}
        mode={isMobile ? 'window' : ziAssistMode}
        onClose={() => setShowZiAssist(false)}
        onModeChange={setZiAssistMode}
        isMobile={isMobile}
        isLowEndDevice={isLowEndDevice}
      />

      {/* Mobile Optimization Indicator - Shows device capabilities */}
      <MobileOptimizationIndicator />
    </div>
  )
}
