'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { useWallpaperStore } from '@/lib/stores/wallpaper-store'
import { useLiquidGlass } from '@/hooks/useLiquidGlass'

// ZiExplorer Content Component - Exact copy of original AppleDesktopEnvironment panel
export function ZiExplorerContent() {
  const { generateIconBackground } = useLiquidGlass()

  const folders = [
    { id: 'documents', name: 'Documents', icon: '/images/icons/documents-folder.png', variant: 'blue' as const, path: '/Documents' },
    { id: 'pictures', name: 'Pictures', icon: '/images/icons/image-folder.png', variant: 'cyan' as const, path: '/Pictures' },
    { id: 'music', name: 'Music', icon: '/images/icons/music-folder.png', variant: 'purple' as const, path: '/Music' }
  ]

  const openFolder = (path: string) => {
    console.log(`Opening folder: ${path}`)
    // Here you would implement folder opening logic - launch file manager
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto space-y-3 pr-2 custom-scrollbar">
        {folders.map((folder) => (
          <motion.div
            key={folder.id}
            whileHover={{ scale: 1.02, x: 8 }}
            whileTap={{ scale: 0.98 }}
            className="flex items-center space-x-4 p-4 rounded-2xl cursor-pointer transition-all duration-300"
            style={{
              background: `
                linear-gradient(145deg,
                  rgba(255, 255, 255, 0.15) 0%,
                  rgba(255, 255, 255, 0.08) 50%,
                  rgba(255, 255, 255, 0.12) 100%
                )
              `,
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderTop: '1px solid rgba(255, 255, 255, 0.3)',
              borderLeft: '1px solid rgba(255, 255, 255, 0.25)',
              boxShadow: `
                0 8px 25px rgba(0, 0, 0, 0.15),
                0 3px 10px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05)
              `,
              backdropFilter: 'blur(15px) saturate(1.2)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = `
                linear-gradient(145deg,
                  rgba(255, 255, 255, 0.25) 0%,
                  rgba(255, 255, 255, 0.15) 50%,
                  rgba(255, 255, 255, 0.20) 100%
                )
              `
              e.currentTarget.style.transform = 'translateY(-2px)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = `
                linear-gradient(145deg,
                  rgba(255, 255, 255, 0.15) 0%,
                  rgba(255, 255, 255, 0.08) 50%,
                  rgba(255, 255, 255, 0.12) 100%
                )
              `
              e.currentTarget.style.transform = 'translateY(0px)'
            }}
            onClick={() => openFolder(folder.path)}
          >
            <div
              className="w-14 h-14 relative rounded-2xl flex items-center justify-center"
              style={{
                ...generateIconBackground({
                  variant: folder.variant,
                  intensity: 1.2,
                  size: 'medium',
                  isActive: false,
                  isHovered: false
                }),
                boxShadow: `
                  0 4px 15px rgba(0, 0, 0, 0.2),
                  inset 0 1px 0 rgba(255, 255, 255, 0.4)
                `
              }}
            >
              <Image
                src={folder.icon}
                alt={folder.name}
                width={36}
                height={36}
                className="object-contain drop-shadow-sm"
              />
            </div>
            <span className="text-white font-medium text-base drop-shadow-lg tracking-wide">
              {folder.name}
            </span>
          </motion.div>
        ))}
      </div>

      {/* Custom Scrollbar Styles for ZiExplorer */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, rgba(139,69,255,0.6) 0%, rgba(59,130,246,0.6) 100%);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, rgba(139,69,255,0.8) 0%, rgba(59,130,246,0.8) 100%);
        }
      `}</style>
    </div>
  )
}

// ZiWidgets Content Component
export function ZiWidgetsContent() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [notes, setNotes] = useState('')
  const { wallpapers, currentWallpaper, setWallpaper, addCustomWallpaper, deleteWallpaper } = useWallpaperStore()
  const [showWallpaperGrid, setShowWallpaperGrid] = useState(false)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const handleWallpaperImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string
        if (imageUrl) {
          const newWallpaper = {
            id: `imported-${Date.now()}`,
            name: file.name.replace(/\.[^/.]+$/, ''),
            path: imageUrl,
            category: 'imported'
          }
          addCustomWallpaper(newWallpaper)
          setWallpaper(newWallpaper.id)
        }
      }
      reader.readAsDataURL(file)
    }
    event.target.value = ''
  }

  const handleWallpaperSelect = (wallpaperId: string) => {
    setWallpaper(wallpaperId)
  }

  const handleWallpaperDelete = (wallpaperId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    if (confirm('Are you sure you want to delete this wallpaper?')) {
      deleteWallpaper(wallpaperId)
    }
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto space-y-4 pr-2 custom-scrollbar">
        {/* Apple 26 Time Display */}
        <div
          className="rounded-3xl p-6 border transition-all duration-300"
          style={{
            background: `
              linear-gradient(145deg,
                rgba(255, 255, 255, 0.20) 0%,
                rgba(255, 255, 255, 0.10) 50%,
                rgba(255, 255, 255, 0.15) 100%
              ),
              linear-gradient(135deg,
                rgba(99, 102, 241, 0.40) 0%,
                rgba(139, 92, 246, 0.36) 25%,
                rgba(110, 140, 160, 0.34) 50%,
                rgba(90, 150, 140, 0.32) 75%,
                rgba(59, 130, 246, 0.30) 100%
              )
            `,
            border: '1.5px solid rgba(255, 255, 255, 0.25)',
            borderTop: '2px solid rgba(255, 255, 255, 0.35)',
            borderLeft: '1.5px solid rgba(255, 255, 255, 0.3)',
            boxShadow: `
              0 15px 35px rgba(0, 0, 0, 0.15),
              0 5px 15px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.3),
              inset 0 -1px 0 rgba(0, 0, 0, 0.05)
            `,
            backdropFilter: 'blur(20px) saturate(1.3)'
          }}
        >
          <h4 className="text-white text-sm font-semibold mb-3 drop-shadow-lg tracking-wide opacity-90">
            Current Time
          </h4>
          <div className="text-3xl font-light text-white mb-2 drop-shadow-xl tracking-wider">
            {currentTime.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            })}
          </div>
          <div className="text-sm text-white/85 drop-shadow-md font-medium tracking-wide">
            {currentTime.toLocaleDateString([], {
              weekday: 'long',
              month: 'short',
              day: 'numeric'
            })}
          </div>
        </div>

        {/* Apple 26 Quick Notes */}
        <div
          className="rounded-3xl p-6"
          style={{
            background: `
              linear-gradient(145deg,
                rgba(255, 255, 255, 0.20) 0%,
                rgba(255, 255, 255, 0.10) 50%,
                rgba(255, 255, 255, 0.15) 100%
              ),
              linear-gradient(135deg,
                rgba(99, 102, 241, 0.40) 0%,
                rgba(139, 92, 246, 0.36) 25%,
                rgba(110, 140, 160, 0.34) 50%,
                rgba(90, 150, 140, 0.32) 75%,
                rgba(59, 130, 246, 0.30) 100%
              )
            `,
            border: '1.5px solid rgba(255, 255, 255, 0.25)',
            borderTop: '2px solid rgba(255, 255, 255, 0.35)',
            borderLeft: '1.5px solid rgba(255, 255, 255, 0.3)',
            boxShadow: `
              0 15px 35px rgba(0, 0, 0, 0.15),
              0 5px 15px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.3),
              inset 0 -1px 0 rgba(0, 0, 0, 0.05)
            `,
            backdropFilter: 'blur(20px) saturate(1.3)'
          }}
        >
          <h4 className="text-white text-sm font-semibold mb-4 drop-shadow-lg tracking-wide opacity-90">
            Quick Notes
          </h4>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Jot down your thoughts..."
            className="w-full h-20 text-white placeholder-white/70 resize-none focus:outline-none text-sm font-medium tracking-wide"
            style={{
              background: `
                linear-gradient(145deg,
                  rgba(0, 0, 0, 0.25) 0%,
                  rgba(0, 0, 0, 0.15) 50%,
                  rgba(0, 0, 0, 0.20) 100%
                )
              `,
              border: '1px solid rgba(255, 255, 255, 0.15)',
              borderRadius: '16px',
              padding: '16px',
              boxShadow: `
                inset 0 2px 8px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1)
              `,
              backdropFilter: 'blur(10px)'
            }}
          />
        </div>

        {/* Weather Widget */}
        <div
          className="rounded-2xl p-4"
          style={{
            background: `
              linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
              linear-gradient(135deg, rgba(99, 102, 241, 0.38) 0%, rgba(139, 92, 246, 0.34) 25%, rgba(110, 140, 160, 0.32) 50%, rgba(90, 150, 140, 0.30) 75%, rgba(59, 130, 246, 0.28) 100%)
            `,
            border: '1px solid rgba(255,255,255,0.2)',
            boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
          }}
        >
          <h4 className="text-white text-sm font-medium mb-3 drop-shadow-md">Weather</h4>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-lg font-semibold text-white drop-shadow-lg">22°C</div>
              <div className="text-xs text-white/80 drop-shadow-md">Partly Cloudy</div>
            </div>
            <div className="text-2xl drop-shadow-lg">⛅</div>
          </div>
        </div>

        {/* Wallpaper Settings */}
        <div
          className="rounded-2xl p-4"
          style={{
            background: `
              linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
              linear-gradient(135deg, rgba(99, 102, 241, 0.38) 0%, rgba(139, 92, 246, 0.34) 25%, rgba(110, 140, 160, 0.32) 50%, rgba(90, 150, 140, 0.30) 75%, rgba(59, 130, 246, 0.28) 100%)
            `,
            border: '1px solid rgba(255,255,255,0.2)',
            boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
          }}
        >
          <h4 className="text-white text-sm font-medium mb-3 drop-shadow-md">Wallpaper</h4>

          {/* Current Wallpaper Preview */}
          <div className="mb-3">
            <div className="text-white text-xs mb-2 opacity-80">Current:</div>
            <div
              className="w-full h-16 rounded-lg border overflow-hidden"
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.2)'
              }}
            />
            <div className="text-white text-xs mt-1 opacity-60">Default Gradient</div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-2">
            <button
              className="w-full bg-gradient-to-r from-green-500/30 to-emerald-500/30 hover:from-green-500/40 hover:to-emerald-500/40 border border-white/30 rounded-xl p-2 text-white text-xs transition-all duration-200 backdrop-blur-sm"
              style={{
                boxShadow: '0 4px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
              }}
            >
              📷 Import Wallpaper
            </button>
            <button
              className="w-full bg-gradient-to-r from-purple-500/30 to-blue-500/30 hover:from-purple-500/40 hover:to-blue-500/40 border border-white/30 rounded-xl p-2 text-white text-xs transition-all duration-200 backdrop-blur-sm"
              style={{
                boxShadow: '0 4px 8px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
              }}
            >
              🎨 Browse Gallery
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
