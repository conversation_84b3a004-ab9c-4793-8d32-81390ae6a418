'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { useWallpaperStore } from '@/lib/stores/wallpaper-store'
import { useLiquidGlass } from '@/hooks/useLiquidGlass'

// ZiExplorer Content Component
export function ZiExplorerContent() {
  const { generateIconBackground } = useLiquidGlass()

  const folders = [
    { id: 'documents', name: 'Documents', icon: '/images/icons/documents-folder.png', variant: 'blue' as const },
    { id: 'pictures', name: 'Pictures', icon: '/images/icons/image-folder.png', variant: 'cyan' as const },
    { id: 'music', name: 'Music', icon: '/images/icons/music-folder.png', variant: 'purple' as const },
    { id: 'downloads', name: 'Downloads', icon: '/images/icons/downloads-folder.png', variant: 'green' as const },
    { id: 'desktop', name: 'Desktop', icon: '/images/icons/desktop-folder.png', variant: 'orange' as const },
    { id: 'videos', name: 'Videos', icon: '/images/icons/video-folder.png', variant: 'pink' as const }
  ]

  const handleFolderClick = (folderId: string) => {
    console.log(`Opening folder: ${folderId}`)
    // Here you would implement folder opening logic
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto space-y-3 pr-2 custom-scrollbar">
        {folders.map((folder) => (
          <motion.div
            key={folder.id}
            whileHover={{ scale: 1.02, x: 5 }}
            whileTap={{ scale: 0.98 }}
            className="flex items-center space-x-4 p-4 rounded-2xl cursor-pointer transition-all duration-200"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
              border: '1px solid rgba(255,255,255,0.15)',
              boxShadow: '0 4px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
            }}
            onClick={() => handleFolderClick(folder.id)}
          >
            <div
              className="w-12 h-12 relative rounded-xl flex items-center justify-center"
              style={generateIconBackground({
                variant: folder.variant,
                intensity: 1.0,
                size: 'small',
                isActive: false,
                isHovered: false
              })}
            >
              <Image
                src={folder.icon}
                alt={folder.name}
                width={32}
                height={32}
                className="object-contain"
              />
            </div>
            <span className="text-white font-medium drop-shadow-md">{folder.name}</span>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

// ZiWidgets Content Component
export function ZiWidgetsContent() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [notes, setNotes] = useState('')
  const { wallpapers, currentWallpaper, setWallpaper, addCustomWallpaper, deleteWallpaper } = useWallpaperStore()
  const [showWallpaperGrid, setShowWallpaperGrid] = useState(false)

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const handleWallpaperImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string
        if (imageUrl) {
          const newWallpaper = {
            id: `imported-${Date.now()}`,
            name: file.name.replace(/\.[^/.]+$/, ''),
            path: imageUrl,
            category: 'imported'
          }
          addCustomWallpaper(newWallpaper)
          setWallpaper(newWallpaper.id)
        }
      }
      reader.readAsDataURL(file)
    }
    event.target.value = ''
  }

  const handleWallpaperSelect = (wallpaperId: string) => {
    setWallpaper(wallpaperId)
  }

  const handleWallpaperDelete = (wallpaperId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    if (confirm('Are you sure you want to delete this wallpaper?')) {
      deleteWallpaper(wallpaperId)
    }
  }

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-y-auto space-y-4 pr-2 custom-scrollbar">
        {/* Time Display */}
        <div
          className="rounded-2xl p-4 border"
          style={{
            background: `
              linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
              linear-gradient(135deg, rgba(99, 102, 241, 0.38) 0%, rgba(139, 92, 246, 0.34) 25%, rgba(110, 140, 160, 0.32) 50%, rgba(90, 150, 140, 0.30) 75%, rgba(59, 130, 246, 0.28) 100%)
            `,
            border: '1px solid rgba(255,255,255,0.2)',
            boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
          }}
        >
          <h4 className="text-white text-sm font-medium mb-2 drop-shadow-md">Current Time</h4>
          <div className="text-2xl font-bold text-white mb-1 drop-shadow-lg">
            {currentTime.toLocaleTimeString([], {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            })}
          </div>
          <div className="text-sm text-white/80 drop-shadow-md">
            {currentTime.toLocaleDateString([], {
              weekday: 'long',
              month: 'short',
              day: 'numeric'
            })}
          </div>
        </div>

        {/* Quick Notes */}
        <div
          className="rounded-2xl p-4"
          style={{
            background: `
              linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
              linear-gradient(135deg, rgba(99, 102, 241, 0.38) 0%, rgba(139, 92, 246, 0.34) 25%, rgba(110, 140, 160, 0.32) 50%, rgba(90, 150, 140, 0.30) 75%, rgba(59, 130, 246, 0.28) 100%)
            `,
            border: '1px solid rgba(255,255,255,0.2)',
            boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
          }}
        >
          <h4 className="text-white text-sm font-medium mb-3 drop-shadow-md">Quick Notes</h4>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Jot down your thoughts..."
            className="w-full h-16 bg-black/20 border border-white/30 rounded-xl p-3 text-white placeholder-white/60 resize-none focus:outline-none focus:ring-2 focus:ring-purple-400/50 text-sm backdrop-blur-sm"
            style={{
              boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.2)'
            }}
          />
        </div>

        {/* Weather Widget */}
        <div
          className="rounded-2xl p-4"
          style={{
            background: `
              linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.05) 100%),
              linear-gradient(135deg, rgba(99, 102, 241, 0.38) 0%, rgba(139, 92, 246, 0.34) 25%, rgba(110, 140, 160, 0.32) 50%, rgba(90, 150, 140, 0.30) 75%, rgba(59, 130, 246, 0.28) 100%)
            `,
            border: '1px solid rgba(255,255,255,0.2)',
            boxShadow: '0 8px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
          }}
        >
          <h4 className="text-white text-sm font-medium mb-3 drop-shadow-md">Weather</h4>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-lg font-semibold text-white drop-shadow-lg">22°C</div>
              <div className="text-xs text-white/80 drop-shadow-md">Partly Cloudy</div>
            </div>
            <div className="text-2xl drop-shadow-lg">⛅</div>
          </div>
        </div>
      </div>
    </div>
  )
}
