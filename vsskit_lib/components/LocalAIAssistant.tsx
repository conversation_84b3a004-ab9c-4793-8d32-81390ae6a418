'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { fallbackAI } from '@/ai/fallback-ai'
import { browserAIManager as enhancedAIManager } from '@/ai/browser-ai-manager'
import { localAIManager } from '@/lib/local-ai-manager'
import { DarkTooltip } from '@/components/DarkTooltip'
import { FileUpload } from '@/components/FileUpload'
import { useLiquidGlass } from '@/hooks/useLiquidGlass'
import { useWindowStore } from '@/lib/stores/window-store'
import Image from 'next/image'

// AI Model interface
interface AIModel {
  id: string
  name: string
  size: string
  description: string
  task: string
  modelId: string
  downloaded: boolean
  pipeline?: any
}


interface Message {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
}

interface LocalAIAssistantProps {
  isOpen?: boolean
  mode?: 'sidebar' | 'window'
  onClose?: () => void
  onModeChange?: (mode: 'sidebar' | 'window') => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

export function LocalAIAssistant({
  isOpen = false,
  mode = 'sidebar',
  onClose,
  onModeChange,
  isMobile = false,
  isLowEndDevice = false
}: LocalAIAssistantProps) {
  const [internalIsOpen, setInternalIsOpen] = useState(false)
  const isVisible = isOpen || internalIsOpen
  const [messages, setMessages] = useState<Message[]>([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isModelLoaded, setIsModelLoaded] = useState(false)
  const [currentModel, setCurrentModel] = useState<any>(null)
  const [selectedModelId, setSelectedModelId] = useState('distilgpt2')
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [isDownloading, setIsDownloading] = useState(false)
  const [showModelManager, setShowModelManager] = useState(false)
  const [availableModels, setAvailableModels] = useState<AIModel[]>([])
  const [currentTask, setCurrentTask] = useState<string>('')
  const [error, setError] = useState<string | null>(null)
  const [uploadedFiles, setUploadedFiles] = useState<Array<{
    file: File
    content: string
    type: 'pdf' | 'image'
    id: string
  }>>([])
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [chatHistory, setChatHistory] = useState<Message[][]>([])
  const [currentChatIndex, setCurrentChatIndex] = useState(0)
  const [showHistory, setShowHistory] = useState(false)
  const [modelDownloadStates, setModelDownloadStates] = useState<Record<string, {
    isDownloading: boolean
    progress: number
    error: string | null
  }>>({})

  // Isolated workspace states
  const [activeWorkspace, setActiveWorkspace] = useState<'chat' | 'history' | 'models' | 'settings'>('chat')

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const { generateIconBackground } = useLiquidGlass()

  // Window tracking for collapse system
  const windowStore = useWindowStore()
  const [ziAssistWindowId, setZiAssistWindowId] = useState<string | null>(null)

  // Initialize AI models
  useEffect(() => {
    initializeAI()
  }, [])

  // Listen for AI assistant open events
  useEffect(() => {
    const handleOpenAI = () => setInternalIsOpen(true)
    window.addEventListener('openApplication', (e: any) => {
      if (e.detail.appName === 'ai-assistant') {
        handleOpenAI()
      }
    })
    return () => window.removeEventListener('openApplication', handleOpenAI)
  }, [])

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Track ZiAssist window state for collapse system
  useEffect(() => {
    if (isVisible) {
      // Add ZiAssist as a window when visible (both window and sidebar modes)
      if (!ziAssistWindowId) {
        const windowId = `ziassist-${mode}-${Date.now()}`

        // Manually add to window store with proper ID
        const newWindow = {
          id: windowId,
          title: `ZiAssist AI Assistant (${mode})`,
          isOpen: true,
          isMinimized: false,
          isMaximized: false,
          position: mode === 'window' ? { x: 100, y: 100 } : { x: 0, y: 0 },
          size: { width: 400, height: 600 }
        }

        windowStore.addWindow(newWindow)
        setZiAssistWindowId(windowId)
        console.log('🤖 ZiAssist added to window store:', { windowId, mode })
      }
    } else if (!isVisible && ziAssistWindowId) {
      // Remove ZiAssist window when closed
      windowStore.removeWindow(ziAssistWindowId)
      setZiAssistWindowId(null)
      console.log('🤖 ZiAssist removed from window store:', ziAssistWindowId)
    }
  }, [isVisible, mode, ziAssistWindowId, windowStore])

  const initializeAI = async () => {
    try {
      setError(null)

      // Initialize both AI managers
      await localAIManager.initialize()

      // Get available models from enhanced browser AI manager
      const models = enhancedAIManager.getAvailableModels()
      setAvailableModels(models)

      // Check which models are already downloaded
      const loadedModels = enhancedAIManager.getLoadedModels()
      if (loadedModels.length > 0) {
        setAvailableModels(prev => prev.map(model => ({
          ...model,
          downloaded: loadedModels.includes(model.id)
        })))
      }

      // Welcome message with enhanced features
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `Hello! I'm ZiAssist, your enhanced AI study companion with local model caching. I can help you with:\n\n• **Advanced AI Models**: Download models locally for faster performance\n• **Study Techniques**: Personalized learning strategies\n• **Document Analysis**: Upload PDFs and images for AI-powered Q&A\n• **Text Summarization**: Intelligent content summarization\n• **Learning Organization**: Study planning and time management\n• **Subject-specific Guidance**: Tailored help for different subjects\n\n🚀 **New Features**:\n• Local model caching for better performance\n• Reduced memory usage with resource-based loading\n• Offline AI capabilities once models are downloaded\n\nDownload an AI model for advanced features, or upload files to start chatting with your documents!`,
        timestamp: new Date()
      }
      setMessages([welcomeMessage])
    } catch (error) {
      console.error('Failed to initialize AI:', error)
      setError('Failed to initialize AI assistant')
    }
  }

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    const currentInput = inputMessage.trim()
    setInputMessage('')
    setIsLoading(true)

    try {
      let response = ''

      // Check if we have a loaded AI model
      const isModelLoaded = enhancedAIManager.isModelLoaded(selectedModelId)

      if (isModelLoaded) {
        // Use enhanced AI model
        const selectedModel = availableModels.find(m => m.id === selectedModelId)

        try {
          if (selectedModel?.task === 'text-generation') {
            response = await enhancedAIManager.generateText(selectedModelId, currentInput, {
              maxLength: 150,
              temperature: 0.7
            })
          } else if (selectedModel?.task === 'question-answering') {
            // For Q&A, check if we have uploaded files for context
            const context = uploadedFiles.map(f => f.content).join('\n\n')
            if (context) {
              response = await enhancedAIManager.answerQuestion(selectedModelId, currentInput, context)
            } else {
              response = await fallbackAI.answerQuestion(currentInput)
            }
          } else if (selectedModel?.task === 'summarization') {
            // If user wants to summarize uploaded content
            const context = uploadedFiles.map(f => f.content).join('\n\n')
            const textToSummarize = context || currentInput
            response = await enhancedAIManager.summarizeText(selectedModelId, textToSummarize)
          } else if (selectedModel?.task === 'feature-extraction') {
            // For embeddings, provide a helpful response
            response = `I can generate embeddings for text similarity and semantic search. The current input "${currentInput}" would be converted to a 384-dimensional vector for comparison with other texts.`
          } else {
            response = await fallbackAI.generateResponse(currentInput)
          }
        } catch (aiError) {
          console.warn('Enhanced AI model failed, using fallback:', aiError)
          // Fallback to intelligent AI with context
          if (uploadedFiles.length > 0) {
            const context = uploadedFiles.map(f => `File: ${f.file.name}\nContent: ${f.content}`).join('\n\n')
            response = await fallbackAI.answerQuestion(currentInput, context)
          } else {
            response = await fallbackAI.generateResponse(currentInput)
          }
        }
      } else {
        // Use fallback AI with file context if available
        if (uploadedFiles.length > 0) {
          const context = uploadedFiles.map(f => `File: ${f.file.name}\nContent: ${f.content}`).join('\n\n')
          response = await fallbackAI.answerQuestion(currentInput, context)
        } else {
          response = await fallbackAI.generateResponse(currentInput)
        }
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, assistantMessage])
    } catch (error) {
      console.error('Error generating response:', error)

      // Fallback to fallback AI if real model fails
      try {
        const fallbackResponse = await fallbackAI.generateResponse(inputMessage)
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: `⚠️ AI model encountered an error, using fallback response:\n\n${fallbackResponse}`,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, assistantMessage])
      } catch (fallbackError) {
        const errorMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'assistant',
          content: 'Sorry, I encountered an error while processing your request. Please try again or download an AI model for better performance.',
          timestamp: new Date()
        }
        setMessages(prev => [...prev, errorMessage])
      }
    } finally {
      setIsLoading(false)
    }
  }



  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const handleClose = () => {
    if (onClose) {
      onClose()
    } else {
      setInternalIsOpen(false)
    }
  }

  const startNewChat = () => {
    // Save current chat to history if it has messages
    if (messages.length > 1) { // More than just welcome message
      const newHistory = [...chatHistory]
      newHistory[currentChatIndex] = [...messages]
      setChatHistory(newHistory)

      // Create new chat
      const newIndex = newHistory.length
      setCurrentChatIndex(newIndex)
    }

    // Reset to welcome message
    initializeAI()
    setUploadedFiles([])
  }

  const clearCurrentChat = () => {
    initializeAI()
    setUploadedFiles([])
  }

  const deleteChat = (chatIndex: number) => {
    console.log('Deleting chat at index:', chatIndex, 'Current chat history length:', chatHistory.length)

    // Ensure we have a proper history array to work with
    let workingHistory = [...chatHistory]

    // If current chat has messages and isn't already in history, add it
    if (messages.length > 1 && (workingHistory.length === 0 || currentChatIndex >= workingHistory.length)) {
      workingHistory.push([...messages])
      if (workingHistory.length === 1) {
        setCurrentChatIndex(0)
      }
    } else if (messages.length > 1 && currentChatIndex >= 0 && currentChatIndex < workingHistory.length) {
      // Update current chat in history
      workingHistory[currentChatIndex] = [...messages]
    }

    console.log('Working with history length:', workingHistory.length, 'Deleting index:', chatIndex)

    // Validate chat index
    if (chatIndex < 0 || chatIndex >= workingHistory.length) {
      console.log('Invalid chat index:', chatIndex)
      return
    }

    // If only one chat, clear everything
    if (workingHistory.length <= 1) {
      console.log('Only one chat remaining, clearing all')
      setChatHistory([])
      setCurrentChatIndex(0)
      clearCurrentChat()
      return
    }

    // Remove the chat at the specified index
    const newHistory = workingHistory.filter((_, index) => index !== chatIndex)
    console.log('New history length after deletion:', newHistory.length)

    // Update chat history immediately
    setChatHistory(newHistory)

    // Adjust current chat index and load appropriate chat
    let newCurrentIndex = currentChatIndex

    if (chatIndex === currentChatIndex) {
      // If deleting current chat, switch to previous or first available
      newCurrentIndex = chatIndex > 0 ? chatIndex - 1 : 0
      console.log('Deleted current chat, switching to index:', newCurrentIndex)
    } else if (chatIndex < currentChatIndex) {
      // If deleting a chat before current, adjust index down
      newCurrentIndex = currentChatIndex - 1
      console.log('Deleted chat before current, adjusting index to:', newCurrentIndex)
    }

    // Ensure new index is valid
    if (newCurrentIndex >= newHistory.length) {
      newCurrentIndex = newHistory.length - 1
    }

    setCurrentChatIndex(newCurrentIndex)

    // Load the appropriate chat messages
    if (newHistory[newCurrentIndex]) {
      setMessages(newHistory[newCurrentIndex])
      console.log('Loaded chat at index:', newCurrentIndex)
    } else {
      console.log('No chat found at index, initializing new chat')
      initializeAI()
    }

    // Add delete confirmation message after a short delay
    setTimeout(() => {
      const deleteMessage: Message = {
        id: Date.now().toString(),
        type: 'system',
        content: `🗑️ Chat ${chatIndex + 1} deleted. ${newHistory.length} chat(s) remaining.`,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, deleteMessage])
    }, 200)
  }

  const switchToChat = (index: number) => {
    // Save current chat
    if (messages.length > 1) {
      const newHistory = [...chatHistory]
      newHistory[currentChatIndex] = [...messages]
      setChatHistory(newHistory)
    }

    // Load selected chat
    setCurrentChatIndex(index)
    if (chatHistory[index]) {
      setMessages(chatHistory[index])
    } else {
      initializeAI()
    }

    // Switch to chat workspace
    setActiveWorkspace('chat')
    setShowHistory(false)
  }

  const renderHeader = () => (
    <div
      className="flex items-center justify-between p-4"
      style={{
        borderBottom: '1px solid rgba(255,255,255,0.2)',
        background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
      }}
    >
      <div className="flex items-start space-x-3">
        <div className="w-10 h-10 relative flex items-start justify-center -mt-1">
          <Image
            src="/images/icons/ZiAssist.png"
            alt="ZiAssist"
            width={40}
            height={40}
            className="object-contain"
          />
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h2 className="text-white font-semibold drop-shadow-lg text-lg">ZiAssist</h2>
            <span className="text-white/40">•</span>
            <span className="text-white/60 text-sm">
              Chat {currentChatIndex + 1} of {Math.max(chatHistory.length, 1)}
            </span>
          </div>
          <div className="flex items-center mt-1 space-x-3">
            <select
              value={selectedModelId}
              onChange={(e) => setSelectedModelId(e.target.value)}
              className="bg-black/20 border border-white/30 rounded-lg px-2 py-1 text-xs text-white focus:outline-none focus:ring-1 focus:ring-purple-400/50"
            >
              {availableModels.map(model => (
                <option key={model.id} value={model.id} className="bg-gray-800">
                  {model.name}
                </option>
              ))}
            </select>
            <div className="flex items-center space-x-2">
              <DarkTooltip content="AI Model Manager" position={mode === 'sidebar' ? 'top' : 'bottom'} forcePosition={mode === 'sidebar'}>
                <button
                  onClick={() => setActiveWorkspace('models')}
                  className="bg-white/10 hover:bg-white/20 p-2 rounded-full backdrop-blur-sm transition-colors border border-white/20"
                  style={{
                    boxShadow: '0 4px 12px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
                  }}
                >
                  <div className="w-5 h-5 relative">
                    <Image
                      src="/images/icons/settings.png"
                      alt="Settings"
                      width={20}
                      height={20}
                      className="object-contain opacity-80 hover:opacity-100 transition-opacity"
                    />
                  </div>
                </button>
              </DarkTooltip>
              {mode === 'sidebar' && onModeChange && (
                <DarkTooltip content="Open in Window" position="top" forcePosition={true}>
                  <button
                    onClick={() => onModeChange('window')}
                    className="bg-white/10 hover:bg-white/20 p-2 rounded-full backdrop-blur-sm transition-colors border border-white/20"
                    style={{
                      boxShadow: '0 4px 12px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
                    }}
                  >
                    <div className="w-5 h-5 relative flex items-center justify-center">
                      <span className="text-white/80 hover:text-white text-sm transition-colors">🗗</span>
                    </div>
                  </button>
                </DarkTooltip>
              )}
              {mode === 'window' && onModeChange && (
                <DarkTooltip content="Dock to Sidebar" position="bottom">
                  <button
                    onClick={() => onModeChange('sidebar')}
                    className="bg-white/10 hover:bg-white/20 p-2 rounded-full backdrop-blur-sm transition-colors border border-white/20"
                    style={{
                      boxShadow: '0 4px 12px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
                    }}
                  >
                    <div className="w-5 h-5 relative flex items-center justify-center">
                      <span className="text-white/80 hover:text-white text-sm transition-colors">📌</span>
                    </div>
                  </button>
                </DarkTooltip>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <DarkTooltip content="Close ZiAssist" position={mode === 'sidebar' ? 'top' : 'bottom'} forcePosition={mode === 'sidebar'}>
          <button
            onClick={handleClose}
            className="w-8 h-8 rounded-full transition-all duration-300 flex items-center justify-center group"
            style={{
              background: `
                radial-gradient(circle at center,
                  rgba(0,0,0,0.85) 0%,
                  rgba(20,20,30,0.90) 30%,
                  rgba(30,30,40,0.95) 60%,
                  rgba(15,15,25,0.88) 100%
                ),
                radial-gradient(circle at 30% 30%, rgba(255,255,255,0.20) 0%, transparent 60%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.12) 0%, transparent 50%),
                linear-gradient(135deg,
                  rgba(239,68,68,0.15) 0%,
                  rgba(239,68,68,0.25) 50%,
                  rgba(239,68,68,0.20) 100%
                )
              `,
              border: '1px solid rgba(255,255,255,0.2)',
              borderTopColor: 'rgba(255,255,255,0.35)',
              borderLeftColor: 'rgba(255,255,255,0.3)',
              borderBottomColor: 'rgba(255,255,255,0.15)',
              borderRightColor: 'rgba(255,255,255,0.2)',
              boxShadow: `
                inset 0 1px 1px rgba(255,255,255,0.4),
                inset 0 -1px 1px rgba(0,0,0,0.2),
                inset 1px 0 1px rgba(255,255,255,0.25),
                inset -1px 0 1px rgba(0,0,0,0.15),
                0 4px 16px rgba(0,0,0,0.4),
                0 2px 8px rgba(0,0,0,0.25),
                0 0 20px rgba(239,68,68,0.2),
                0 0 40px rgba(255,255,255,0.08)
              `,
              backdropFilter: 'blur(25px) saturate(1.3) brightness(1.1)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = `
                radial-gradient(circle at center,
                  rgba(0,0,0,0.90) 0%,
                  rgba(25,25,35,0.95) 30%,
                  rgba(35,35,45,1) 60%,
                  rgba(20,20,30,0.92) 100%
                ),
                radial-gradient(circle at 30% 30%, rgba(255,255,255,0.25) 0%, transparent 60%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.15) 0%, transparent 50%),
                linear-gradient(135deg,
                  rgba(239,68,68,0.4) 0%,
                  rgba(239,68,68,0.6) 50%,
                  rgba(239,68,68,0.5) 100%
                )
              `
              e.currentTarget.style.boxShadow = `
                inset 0 1px 1px rgba(255,255,255,0.4),
                inset 0 -1px 1px rgba(0,0,0,0.2),
                inset 1px 0 1px rgba(255,255,255,0.25),
                inset -1px 0 1px rgba(0,0,0,0.15),
                0 4px 16px rgba(0,0,0,0.4),
                0 2px 8px rgba(0,0,0,0.25),
                0 0 25px rgba(239,68,68,0.7),
                0 0 50px rgba(239,68,68,0.5),
                0 0 75px rgba(239,68,68,0.4),
                0 0 100px rgba(255,0,0,0.3)
              `
              e.currentTarget.style.borderColor = 'rgba(239,68,68,0.6)'
              e.currentTarget.style.borderTopColor = 'rgba(239,68,68,0.8)'
              e.currentTarget.style.borderLeftColor = 'rgba(239,68,68,0.7)'
              e.currentTarget.style.borderBottomColor = 'rgba(239,68,68,0.4)'
              e.currentTarget.style.borderRightColor = 'rgba(239,68,68,0.5)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = `
                radial-gradient(circle at center,
                  rgba(0,0,0,0.85) 0%,
                  rgba(20,20,30,0.90) 30%,
                  rgba(30,30,40,0.95) 60%,
                  rgba(15,15,25,0.88) 100%
                ),
                radial-gradient(circle at 30% 30%, rgba(255,255,255,0.20) 0%, transparent 60%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.12) 0%, transparent 50%),
                linear-gradient(135deg,
                  rgba(239,68,68,0.15) 0%,
                  rgba(239,68,68,0.25) 50%,
                  rgba(239,68,68,0.20) 100%
                )
              `
              e.currentTarget.style.boxShadow = `
                inset 0 1px 1px rgba(255,255,255,0.4),
                inset 0 -1px 1px rgba(0,0,0,0.2),
                inset 1px 0 1px rgba(255,255,255,0.25),
                inset -1px 0 1px rgba(0,0,0,0.15),
                0 4px 16px rgba(0,0,0,0.4),
                0 2px 8px rgba(0,0,0,0.25),
                0 0 20px rgba(239,68,68,0.2),
                0 0 40px rgba(255,255,255,0.08)
              `
              e.currentTarget.style.borderColor = 'rgba(255,255,255,0.2)'
              e.currentTarget.style.borderTopColor = 'rgba(255,255,255,0.35)'
              e.currentTarget.style.borderLeftColor = 'rgba(255,255,255,0.3)'
              e.currentTarget.style.borderBottomColor = 'rgba(255,255,255,0.15)'
              e.currentTarget.style.borderRightColor = 'rgba(255,255,255,0.2)'
            }}
          >
            {/* Liquid Glass Overlay */}
            <div
              className="absolute inset-0 rounded-full pointer-events-none"
              style={{
                background: `
                  radial-gradient(circle at 25% 25%, rgba(255,255,255,0.15) 0%, transparent 50%),
                  radial-gradient(circle at 75% 75%, rgba(255,255,255,0.08) 0%, transparent 40%),
                  linear-gradient(135deg,
                    rgba(255,255,255,0.10) 0%,
                    rgba(255,255,255,0.05) 50%,
                    transparent 100%
                  )
                `,
                border: '1px solid rgba(255,255,255,0.12)',
                borderTopColor: 'rgba(255,255,255,0.20)',
                borderLeftColor: 'rgba(255,255,255,0.16)',
                borderBottomColor: 'rgba(255,255,255,0.06)',
                borderRightColor: 'rgba(255,255,255,0.08)'
              }}
            />

            <svg
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              className="text-white group-hover:text-white/95 transition-colors relative z-10"
              style={{
                filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.6))'
              }}
            >
              <path
                d="M3 3L11 11M3 11L11 3"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </svg>
          </button>
        </DarkTooltip>
      </div>
    </div>
  )

  // Isolated Workspace Components
  const renderChatWorkspace = () => (
    <div className="flex-1 overflow-y-auto p-4 space-y-4 ziassist-scrollbar">
      {messages.map((message) => (
        <motion.div
          key={message.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className={`flex ${
            message.type === 'user'
              ? 'justify-end'
              : message.type === 'system'
                ? 'justify-center'
                : 'justify-center'
          }`}
        >
          <div
            className={`max-w-[90%] p-4 rounded-2xl relative overflow-hidden ${
              message.type === 'user'
                ? 'bg-purple-500/70 text-white'
                : message.type === 'system'
                  ? 'bg-yellow-500/20 text-yellow-200 border border-yellow-500/30'
                  : 'text-slate-600'
            }`}
            style={
              message.type === 'assistant'
                ? {
                    background: `
                      radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.008) 0%, transparent 80%),
                      radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.006) 0%, transparent 75%),
                      radial-gradient(circle at 30% 80%, rgba(59, 130, 246, 0.01) 0%, transparent 70%),
                      radial-gradient(circle at 70% 70%, rgba(16, 185, 129, 0.005) 0%, transparent 65%),
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 1) 0%,
                        rgba(255, 255, 255, 1) 15%,
                        rgba(255, 255, 255, 1) 30%,
                        rgba(255, 255, 255, 1) 45%,
                        rgba(255, 255, 255, 1) 60%,
                        rgba(255, 255, 255, 1) 75%,
                        rgba(255, 255, 255, 1) 90%,
                        rgba(255, 255, 255, 1) 100%
                      )
                    `,
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    borderTopColor: 'rgba(255, 255, 255, 0.5)',
                    borderRightColor: 'rgba(255, 255, 255, 0.25)',
                    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
                    borderLeftColor: 'rgba(255, 255, 255, 0.3)',
                    boxShadow: `
                      0 4px 16px rgba(0, 0, 0, 0.04),
                      0 2px 8px rgba(0, 0, 0, 0.03),
                      0 1px 4px rgba(0, 0, 0, 0.02),
                      inset 0 1px 0 rgba(255, 255, 255, 0.8),
                      inset 0 -1px 0 rgba(255, 255, 255, 0.1)
                    `,
                    backdropFilter: 'blur(12px) saturate(1.15) brightness(1.08)',
                    // Minimal paper texture effect for accessibility
                    backgroundImage: `
                      radial-gradient(circle at 2px 2px, rgba(0, 0, 0, 0.001) 1px, transparent 0),
                      radial-gradient(circle at 6px 6px, rgba(139, 92, 246, 0.0008) 0.5px, transparent 0)
                    `,
                    backgroundSize: '24px 24px, 32px 32px',
                    backgroundPosition: '0 0, 12px 12px'
                  }
                : {}
            }
          >
            {/* Light & Happy Paper Texture Overlay for Assistant Messages */}
            {message.type === 'assistant' && (
              <div
                className="absolute inset-0 pointer-events-none rounded-2xl"
                style={{
                  background: `
                    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.6) 0%, transparent 70%),
                    radial-gradient(circle at 75% 25%, rgba(255,255,255,0.5) 0%, transparent 65%),
                    radial-gradient(circle at 25% 75%, rgba(255,255,255,0.4) 0%, transparent 60%),
                    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.45) 0%, transparent 55%),
                    linear-gradient(45deg,
                      transparent 0%,
                      rgba(255,255,255,0.4) 25%,
                      rgba(255,255,255,0.5) 50%,
                      rgba(255,255,255,0.4) 75%,
                      transparent 100%
                    ),
                    linear-gradient(-45deg,
                      transparent 0%,
                      rgba(255,255,255,0.2) 40%,
                      rgba(255,255,255,0.3) 50%,
                      rgba(255,255,255,0.2) 60%,
                      transparent 100%
                    )
                  `,
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderTopColor: 'rgba(255,255,255,0.4)',
                  borderLeftColor: 'rgba(255,255,255,0.3)',
                  borderBottomColor: 'rgba(255,255,255,0.15)',
                  borderRightColor: 'rgba(255,255,255,0.2)'
                }}
              />
            )}

            <p className={`text-sm whitespace-pre-line relative z-10 ${
              message.type === 'assistant' ? 'text-slate-600 font-medium' : ''
            }`}>
              {message.content}
            </p>
            <p className={`text-xs mt-1 relative z-10 ${
              message.type === 'assistant' ? 'text-slate-500 opacity-80' : 'opacity-60'
            }`}>
              {message.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
        </motion.div>
      ))}

      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex justify-center"
        >
          <div
            className="text-slate-600 p-4 rounded-2xl relative overflow-hidden"
            style={{
              background: `
                radial-gradient(circle at 20% 30%, rgba(139, 92, 246, 0.008) 0%, transparent 80%),
                radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.006) 0%, transparent 75%),
                radial-gradient(circle at 30% 80%, rgba(59, 130, 246, 0.01) 0%, transparent 70%),
                radial-gradient(circle at 70% 70%, rgba(16, 185, 129, 0.005) 0%, transparent 65%),
                linear-gradient(135deg,
                  rgba(255, 255, 255, 1) 0%,
                  rgba(255, 255, 255, 1) 15%,
                  rgba(255, 255, 255, 1) 30%,
                  rgba(255, 255, 255, 1) 45%,
                  rgba(255, 255, 255, 1) 60%,
                  rgba(255, 255, 255, 1) 75%,
                  rgba(255, 255, 255, 1) 90%,
                  rgba(255, 255, 255, 1) 100%
                )
              `,
              border: '1px solid rgba(255, 255, 255, 0.3)',
              boxShadow: `
                0 4px 16px rgba(0, 0, 0, 0.04),
                0 2px 8px rgba(0, 0, 0, 0.03),
                inset 0 1px 0 rgba(255,255,255,0.8)
              `,
              backdropFilter: 'blur(12px) saturate(1.15) brightness(1.08)',
              // Minimal paper texture effect for accessibility
              backgroundImage: `
                radial-gradient(circle at 2px 2px, rgba(0, 0, 0, 0.001) 1px, transparent 0),
                radial-gradient(circle at 6px 6px, rgba(139, 92, 246, 0.0008) 0.5px, transparent 0)
              `,
              backgroundSize: '24px 24px, 32px 32px'
            }}
          >
            {/* Light & Happy Paper Texture Overlay */}
            <div
              className="absolute inset-0 pointer-events-none rounded-2xl"
              style={{
                background: `
                  radial-gradient(circle at 25% 25%, rgba(255,255,255,0.6) 0%, transparent 70%),
                  radial-gradient(circle at 75% 25%, rgba(255,255,255,0.5) 0%, transparent 65%),
                  radial-gradient(circle at 25% 75%, rgba(255,255,255,0.4) 0%, transparent 60%),
                  radial-gradient(circle at 75% 75%, rgba(255,255,255,0.45) 0%, transparent 55%),
                  linear-gradient(45deg,
                    transparent 0%,
                    rgba(255,255,255,0.4) 25%,
                    rgba(255,255,255,0.5) 50%,
                    rgba(255,255,255,0.4) 75%,
                    transparent 100%
                  )
                `,
                border: '1px solid rgba(255,255,255,0.2)',
                borderTopColor: 'rgba(255,255,255,0.4)'
              }}
            />

            <div className="flex space-x-1 relative z-10">
              <div className="w-2 h-2 bg-slate-500/70 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-slate-500/70 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-slate-500/70 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
          </div>
        </motion.div>
      )}

      <div ref={messagesEndRef} />
    </div>
  )

  const renderHistoryWorkspace = () => (
    <div className="flex-1 overflow-y-auto p-4 ziassist-scrollbar">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-white font-medium text-lg">Chat History</h3>
          <button
            onClick={() => {
              startNewChat()
              setActiveWorkspace('chat')
            }}
            className="bg-purple-500/70 hover:bg-purple-500/90 px-3 py-1 rounded-lg text-white text-sm transition-colors backdrop-blur-sm border border-white/20"
            style={{
              boxShadow: '0 4px 12px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
            }}
          >
            New Chat
          </button>
        </div>

        <div className="space-y-3">
          {(() => {
            // Create display history that properly reflects current state
            let displayHistory = [...chatHistory]

            // If current chat has messages and should be included in display
            if (messages.length > 1) {
              if (displayHistory.length === 0) {
                // No history yet, show current chat as first item
                displayHistory = [messages]
              } else if (currentChatIndex >= 0 && currentChatIndex < displayHistory.length) {
                // Update current chat in existing history
                displayHistory[currentChatIndex] = [...messages]
              } else if (currentChatIndex >= displayHistory.length) {
                // Current chat is beyond history, add it
                displayHistory.push([...messages])
              }
            }

            // Show empty state if no chats
            if (displayHistory.length === 0) {
              return (
                <div className="text-center py-8">
                  <div className="text-white/60 text-sm">No previous chats</div>
                  <div className="text-white/40 text-xs mt-1">Start a conversation to see your chat history here</div>
                </div>
              )
            }

            return displayHistory.map((chat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-4 rounded-xl border transition-all ${
                  index === currentChatIndex
                    ? 'bg-purple-500/30 border-purple-500/50 shadow-lg'
                    : 'bg-white/5 hover:bg-white/10 border-white/10 hover:border-white/20'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div
                    className="flex-1 cursor-pointer"
                    onClick={() => switchToChat(index)}
                  >
                    <div className="text-white text-sm font-medium">
                      Chat {index + 1} {index === currentChatIndex && '(Current)'}
                    </div>
                    <div className="text-white/60 text-xs mt-1">
                      {chat.length > 1 ? chat[1].content.substring(0, 100) + '...' : 'Empty chat'}
                    </div>
                    <div className="text-white/40 text-xs mt-2">
                      {chat.length - 1} messages • {chat[0]?.timestamp.toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-3">
                    {index === currentChatIndex && (
                      <div className="text-purple-400 text-xs">
                        ●
                      </div>
                    )}
                    <DarkTooltip content="Delete this chat" position={mode === 'sidebar' ? 'left' : 'top'} forcePosition={mode === 'sidebar'}>
                      <motion.button
                        onClick={(e) => {
                          e.stopPropagation()
                          deleteChat(index)
                        }}
                        className="w-8 h-8 rounded-full backdrop-blur-sm transition-all duration-300 border-2 border-red-400/40 hover:border-red-400/60"
                        style={{
                          background: `
                            radial-gradient(circle at 30% 30%, rgba(239, 68, 68, 0.3) 0%, transparent 70%),
                            radial-gradient(circle at 70% 70%, rgba(220, 38, 38, 0.25) 0%, transparent 60%),
                            linear-gradient(135deg,
                              rgba(255, 255, 255, 0.15) 0%,
                              rgba(255, 255, 255, 0.08) 50%,
                              rgba(255, 255, 255, 0.12) 100%
                            )
                          `,
                          boxShadow: `
                            0 4px 16px rgba(239, 68, 68, 0.2),
                            0 2px 8px rgba(0,0,0,0.15),
                            inset 0 1px 0 rgba(255,255,255,0.2),
                            inset 0 -1px 0 rgba(0,0,0,0.08),
                            0 0 12px rgba(239, 68, 68, 0.1)
                          `,
                          backdropFilter: 'blur(8px) saturate(1.1) brightness(1.05)'
                        }}
                        whileHover={{
                          scale: 1.1,
                          style: {
                            background: `
                              radial-gradient(circle at 30% 30%, rgba(239, 68, 68, 0.4) 0%, transparent 70%),
                              radial-gradient(circle at 70% 70%, rgba(220, 38, 38, 0.35) 0%, transparent 60%),
                              linear-gradient(135deg,
                                rgba(255, 255, 255, 0.2) 0%,
                                rgba(255, 255, 255, 0.12) 50%,
                                rgba(255, 255, 255, 0.16) 100%
                              )
                            `,
                            boxShadow: `
                              0 6px 20px rgba(239, 68, 68, 0.3),
                              0 3px 12px rgba(0,0,0,0.2),
                              inset 0 1px 0 rgba(255,255,255,0.25),
                              inset 0 -1px 0 rgba(0,0,0,0.1),
                              0 0 16px rgba(239, 68, 68, 0.15)
                            `
                          },
                          transition: { type: "spring", stiffness: 400, damping: 25 }
                        }}
                        whileTap={{
                          scale: 0.9,
                          transition: { type: "spring", stiffness: 600, damping: 30 }
                        }}
                      >
                        <div className="w-4 h-4 relative mx-auto">
                          <Image
                            src="/images/icons/bin.png"
                            alt="Delete"
                            width={16}
                            height={16}
                            className="object-contain opacity-90 hover:opacity-100 transition-opacity"
                          />
                        </div>
                      </motion.button>
                    </DarkTooltip>
                  </div>
                </div>
              </motion.div>
            ))
          })()}
        </div>
      </div>
    </div>
  )

  const renderModelsWorkspace = () => (
    <div className="flex-1 overflow-y-auto p-4 ziassist-scrollbar">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-white font-medium text-lg">AI Model Manager</h3>
          <div className="text-white/60 text-xs">
            {availableModels.filter(m => m.downloaded).length} of {availableModels.length} models loaded
          </div>
        </div>

        <div className="space-y-3">
          {availableModels.map((model, index) => {
            const downloadState = modelDownloadStates[model.id]
            const isDownloading = downloadState?.isDownloading || false
            const progress = downloadState?.progress || 0
            const error = downloadState?.error

            return (
              <motion.div
                key={model.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-4 rounded-xl border transition-all ${
                  error
                    ? 'border-red-500/50 bg-red-500/10'
                    : model.downloaded
                      ? 'border-green-500/50 bg-green-500/10'
                      : 'border-white/10 bg-white/5'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <div className="text-white text-sm font-medium">{model.name}</div>
                      {model.downloaded && (
                        <span className="bg-green-500/70 text-white text-xs px-2 py-0.5 rounded-full">
                          ✅ Loaded
                        </span>
                      )}
                      {selectedModelId === model.id && (
                        <span className="bg-purple-500/70 text-white text-xs px-2 py-0.5 rounded-full">
                          Active
                        </span>
                      )}
                    </div>
                    <div className="text-white/60 text-xs mt-1">{model.description}</div>
                    <div className="text-white/40 text-xs mt-2 space-x-4">
                      <span>Size: {model.size}</span>
                      <span>Task: {model.task}</span>
                      <span>ID: {model.modelId}</span>
                    </div>
                    {error && (
                      <div className="text-red-400 text-xs mt-2 p-2 bg-red-500/10 rounded-lg">
                        ❌ Error: {error}
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col space-y-2">
                    <button
                      onClick={() => downloadModel(model.id)}
                      disabled={isDownloading}
                      className={`px-4 py-2 rounded-lg text-white text-sm transition-colors ${
                        model.downloaded
                          ? 'bg-green-500/70 hover:bg-green-500/90'
                          : isDownloading
                            ? 'bg-white/10 cursor-not-allowed'
                            : error
                              ? 'bg-red-500/70 hover:bg-red-500/90'
                              : 'bg-purple-500/70 hover:bg-purple-500/90'
                      }`}
                    >
                      {model.downloaded ? 'Loaded' : isDownloading ? 'Downloading...' : error ? 'Retry' : 'Download'}
                    </button>
                    {model.downloaded && (
                      <button
                        onClick={() => setSelectedModelId(model.id)}
                        className={`px-4 py-1 rounded-lg text-white text-xs transition-colors ${
                          selectedModelId === model.id
                            ? 'bg-purple-500/70'
                            : 'bg-white/10 hover:bg-white/20'
                        }`}
                      >
                        {selectedModelId === model.id ? 'Active' : 'Select'}
                      </button>
                    )}
                  </div>
                </div>

                {/* Individual Progress Bar */}
                {isDownloading && (
                  <div className="mt-3">
                    <div className="flex justify-between text-xs text-white/80 mb-2">
                      <span>Downloading {model.name}...</span>
                      <span>{progress}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>
                )}
              </motion.div>
            )
          })}
        </div>
      </div>
    </div>
  )

  const renderActiveWorkspace = () => {
    switch (activeWorkspace) {
      case 'chat':
        return renderChatWorkspace()
      case 'history':
        return renderHistoryWorkspace()
      case 'models':
        return renderModelsWorkspace()
      default:
        return renderChatWorkspace()
    }
  }

  const renderInput = () => (
    <div
      className="p-4 space-y-3"
      style={{
        borderTop: '1px solid rgba(255,255,255,0.2)',
        background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
      }}
    >
      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <div className="text-white/80 text-xs font-medium">Uploaded Files:</div>
          <div className="flex flex-wrap gap-2">
            {uploadedFiles.map((fileData) => (
              <div
                key={fileData.id}
                className="flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-1 border border-white/20"
              >
                <div className="w-4 h-4 relative">
                  <Image
                    src={fileData.type === 'pdf' ? '/images/icons/pdf_reader.png' : '/images/icons/image_viewer.png'}
                    alt={fileData.type}
                    width={16}
                    height={16}
                    className="object-contain"
                  />
                </div>
                <span className="text-white/90 text-xs truncate max-w-[120px]">
                  {fileData.file.name}
                </span>
                <button
                  onClick={() => removeFile(fileData.id)}
                  className="text-white/60 hover:text-white/90 text-xs"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* File Upload Area */}
      {showFileUpload && (
        <div className="mb-3">
          <FileUpload
            onFileUpload={handleFileUpload}
            disabled={isLoading}
          />
        </div>
      )}



      {/* Input Area */}
      <div className="flex space-x-2">
        <DarkTooltip content="Upload files" position={mode === 'sidebar' ? 'top' : 'bottom'} forcePosition={mode === 'sidebar'}>
          <button
            onClick={() => setShowFileUpload(!showFileUpload)}
            className="bg-black/30 hover:bg-black/40 p-2 rounded-xl text-white transition-colors backdrop-blur-sm border border-white/20"
            style={{
              boxShadow: '0 4px 12px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
            }}
          >
            📎
          </button>
        </DarkTooltip>
        <input
          type="text"
          value={inputMessage}
          onChange={(e) => setInputMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={uploadedFiles.length > 0 ? "Ask about your uploaded files..." : "Ask me anything about studying..."}
          className="flex-1 bg-black/20 border border-white/30 rounded-xl px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400/50 backdrop-blur-sm"
          style={{
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.2)'
          }}
          disabled={isLoading}
        />
        <button
          onClick={sendMessage}
          disabled={isLoading || !inputMessage.trim()}
          className="bg-purple-600/80 hover:bg-purple-600/90 disabled:bg-black/20 disabled:text-white/30 px-3 py-2 rounded-xl text-white transition-colors backdrop-blur-sm border border-white/20 text-sm"
          style={{
            boxShadow: '0 4px 12px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
          }}
        >
          Send
        </button>
      </div>
    </div>
  )

  const handleFileUpload = (file: File, content: string, type: 'pdf' | 'image') => {
    const fileData = {
      file,
      content,
      type,
      id: Date.now().toString()
    }

    setUploadedFiles(prev => [...prev, fileData])
    setShowFileUpload(false)

    const uploadMessage: Message = {
      id: Date.now().toString(),
      type: 'system',
      content: `📎 **File Uploaded Successfully!**\n\n**${file.name}** (${type.toUpperCase()})\n\nI can now answer questions about this ${type === 'pdf' ? 'document' : 'image'}. Try asking:\n• "What is this ${type === 'pdf' ? 'document' : 'image'} about?"\n• "Summarize the main points"\n• "Answer questions based on this content"\n\n${uploadedFiles.length + 1} file(s) total in context.`,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, uploadMessage])
  }

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId))

    const removeMessage: Message = {
      id: Date.now().toString(),
      type: 'system',
      content: `🗑️ File removed from context. ${uploadedFiles.length - 1} file(s) remaining.`,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, removeMessage])
  }

  const downloadModel = async (modelId: string) => {
    // Set individual model download state
    setModelDownloadStates(prev => ({
      ...prev,
      [modelId]: {
        isDownloading: true,
        progress: 0,
        error: null
      }
    }))

    try {
      const success = await enhancedAIManager.downloadModel(modelId, (progress: number) => {
        setModelDownloadStates(prev => ({
          ...prev,
          [modelId]: {
            ...prev[modelId],
            progress
          }
        }))
      })

      if (success) {
        // Update model status
        setAvailableModels(prev => prev.map(model =>
          model.id === modelId
            ? { ...model, downloaded: true }
            : model
        ))

        setSelectedModelId(modelId)
        setIsModelLoaded(true)

        const model = availableModels.find(m => m.id === modelId)
        const successMessage: Message = {
          id: Date.now().toString(),
          type: 'system',
          content: `✅ **Successfully downloaded and loaded ${model?.name}!**\n\n🚀 **Enhanced Features Now Available:**\n• Local model caching for faster performance\n• Reduced browser memory usage\n• Offline AI capabilities\n• Task: ${model?.task}\n\n**What you can do now:**\n• Ask complex questions\n• Upload files for AI analysis\n• Generate creative content\n• Get intelligent summaries\n\nTry asking me something or upload a file to analyze!`,
          timestamp: new Date()
        }
        setMessages(prev => [...prev, successMessage])

        // Clear download state
        setModelDownloadStates(prev => ({
          ...prev,
          [modelId]: {
            isDownloading: false,
            progress: 100,
            error: null
          }
        }))
      } else {
        throw new Error('Download failed')
      }
    } catch (error) {
      console.error('Model download failed:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'

      // Set error state for this model
      setModelDownloadStates(prev => ({
        ...prev,
        [modelId]: {
          isDownloading: false,
          progress: 0,
          error: errorMessage
        }
      }))

      const systemMessage: Message = {
        id: Date.now().toString(),
        type: 'system',
        content: `❌ **Failed to download model ${modelId}**\n\n**Error:** ${errorMessage}\n\n**Possible causes:**\n• Network connectivity issues\n• Server-side download problems\n• Browser compatibility issues\n• Insufficient storage space\n• Model not available\n\n**Solutions:**\n• Check your internet connection\n• Try again in a few minutes\n• Clear browser cache and retry\n• Use a different browser\n\n💡 **The intelligent fallback AI is still available** for assistance with your studies and uploaded files.`,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, systemMessage])
    }
  }

  if (!isVisible) return null

  // Sidebar Mode
  if (mode === 'sidebar') {
    return (
      <AnimatePresence>
        <motion.div
          initial={{ x: '100%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '100%', opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="fixed right-8 top-24 bottom-32 w-96 z-50"
        >
          {/* Floating Bubble Control Buttons */}
          <div className="absolute -left-14 top-4 flex flex-col space-y-3 z-10">
            <DarkTooltip content="View chat history" position="left" forcePosition={true}>
              <motion.button
                onClick={() => setActiveWorkspace('history')}
                className={`w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300 border-2 ${
                  activeWorkspace === 'history'
                    ? 'border-purple-400/60'
                    : 'border-white/30'
                }`}
                style={{
                  background: activeWorkspace === 'history'
                    ? `
                      radial-gradient(ellipse 120% 80% at 20% 30%, rgba(147, 51, 234, 0.45) 0%, transparent 60%),
                      radial-gradient(ellipse 100% 120% at 80% 20%, rgba(139, 92, 246, 0.35) 0%, transparent 55%),
                      radial-gradient(ellipse 90% 110% at 30% 80%, rgba(168, 85, 247, 0.3) 0%, transparent 50%),
                      radial-gradient(ellipse 110% 90% at 70% 70%, rgba(124, 58, 237, 0.25) 0%, transparent 45%),
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 0.4) 0%,
                        rgba(255, 255, 255, 0.25) 25%,
                        rgba(255, 255, 255, 0.15) 50%,
                        rgba(255, 255, 255, 0.3) 75%,
                        rgba(255, 255, 255, 0.35) 100%
                      ),
                      conic-gradient(from 45deg at 50% 50%,
                        rgba(147, 51, 234, 0.08) 0deg,
                        transparent 90deg,
                        rgba(139, 92, 246, 0.06) 180deg,
                        transparent 270deg,
                        rgba(147, 51, 234, 0.08) 360deg
                      )
                    `
                    : `
                      radial-gradient(ellipse 120% 80% at 25% 25%, rgba(255, 255, 255, 0.3) 0%, transparent 65%),
                      radial-gradient(ellipse 100% 120% at 75% 25%, rgba(255, 255, 255, 0.25) 0%, transparent 60%),
                      radial-gradient(ellipse 90% 110% at 25% 75%, rgba(255, 255, 255, 0.2) 0%, transparent 55%),
                      radial-gradient(ellipse 110% 90% at 75% 75%, rgba(255, 255, 255, 0.18) 0%, transparent 50%),
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 0.25) 0%,
                        rgba(255, 255, 255, 0.15) 25%,
                        rgba(255, 255, 255, 0.08) 50%,
                        rgba(255, 255, 255, 0.18) 75%,
                        rgba(255, 255, 255, 0.22) 100%
                      ),
                      conic-gradient(from 45deg at 50% 50%,
                        rgba(255, 255, 255, 0.05) 0deg,
                        transparent 90deg,
                        rgba(255, 255, 255, 0.03) 180deg,
                        transparent 270deg,
                        rgba(255, 255, 255, 0.05) 360deg
                      )
                    `,
                  boxShadow: activeWorkspace === 'history'
                    ? `
                      0 12px 40px rgba(147, 51, 234, 0.25),
                      0 6px 20px rgba(139, 92, 246, 0.15),
                      0 3px 10px rgba(0,0,0,0.2),
                      inset 0 3px 6px rgba(255,255,255,0.4),
                      inset 0 -3px 6px rgba(147, 51, 234, 0.1),
                      inset 1px 1px 2px rgba(255,255,255,0.3),
                      inset -1px -1px 2px rgba(0,0,0,0.1),
                      0 0 30px rgba(147, 51, 234, 0.2),
                      0 0 60px rgba(139, 92, 246, 0.08)
                    `
                    : `
                      0 8px 32px rgba(0,0,0,0.15),
                      0 4px 16px rgba(0,0,0,0.1),
                      0 2px 8px rgba(0,0,0,0.08),
                      inset 0 3px 6px rgba(255,255,255,0.35),
                      inset 0 -3px 6px rgba(0,0,0,0.05),
                      inset 1px 1px 2px rgba(255,255,255,0.25),
                      inset -1px -1px 2px rgba(0,0,0,0.08),
                      0 0 20px rgba(255,255,255,0.1)
                    `,
                  backdropFilter: 'blur(20px) saturate(1.4) brightness(1.2) contrast(1.1)',
                  borderRadius: '50%',
                  border: 'none',
                  outline: activeWorkspace === 'history'
                    ? '1px solid rgba(147, 51, 234, 0.3)'
                    : '1px solid rgba(255, 255, 255, 0.2)'
                }}
                whileHover={{
                  scale: 1.1,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { type: "spring", stiffness: 600, damping: 30 }
                }}
              >
                <div className="w-6 h-6 relative mx-auto">
                  <Image
                    src="/images/icons/history.png"
                    alt="History"
                    width={24}
                    height={24}
                    className="object-contain opacity-90 hover:opacity-100 transition-opacity"
                  />
                </div>
              </motion.button>
            </DarkTooltip>
            <DarkTooltip content="Start new chat" position="left" forcePosition={true}>
              <motion.button
                onClick={() => {
                  startNewChat()
                  setActiveWorkspace('chat')
                }}
                className={`w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300 border-2 ${
                  activeWorkspace === 'chat'
                    ? 'border-purple-400/60'
                    : 'border-white/30'
                }`}
                style={{
                  background: activeWorkspace === 'chat'
                    ? `
                      radial-gradient(ellipse 120% 80% at 20% 30%, rgba(147, 51, 234, 0.45) 0%, transparent 60%),
                      radial-gradient(ellipse 100% 120% at 80% 20%, rgba(139, 92, 246, 0.35) 0%, transparent 55%),
                      radial-gradient(ellipse 90% 110% at 30% 80%, rgba(168, 85, 247, 0.3) 0%, transparent 50%),
                      radial-gradient(ellipse 110% 90% at 70% 70%, rgba(124, 58, 237, 0.25) 0%, transparent 45%),
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 0.4) 0%,
                        rgba(255, 255, 255, 0.25) 25%,
                        rgba(255, 255, 255, 0.15) 50%,
                        rgba(255, 255, 255, 0.3) 75%,
                        rgba(255, 255, 255, 0.35) 100%
                      ),
                      conic-gradient(from 45deg at 50% 50%,
                        rgba(147, 51, 234, 0.08) 0deg,
                        transparent 90deg,
                        rgba(139, 92, 246, 0.06) 180deg,
                        transparent 270deg,
                        rgba(147, 51, 234, 0.08) 360deg
                      )
                    `
                    : `
                      radial-gradient(ellipse 120% 80% at 25% 25%, rgba(255, 255, 255, 0.3) 0%, transparent 65%),
                      radial-gradient(ellipse 100% 120% at 75% 25%, rgba(255, 255, 255, 0.25) 0%, transparent 60%),
                      radial-gradient(ellipse 90% 110% at 25% 75%, rgba(255, 255, 255, 0.2) 0%, transparent 55%),
                      radial-gradient(ellipse 110% 90% at 75% 75%, rgba(255, 255, 255, 0.18) 0%, transparent 50%),
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 0.25) 0%,
                        rgba(255, 255, 255, 0.15) 25%,
                        rgba(255, 255, 255, 0.08) 50%,
                        rgba(255, 255, 255, 0.18) 75%,
                        rgba(255, 255, 255, 0.22) 100%
                      ),
                      conic-gradient(from 45deg at 50% 50%,
                        rgba(255, 255, 255, 0.05) 0deg,
                        transparent 90deg,
                        rgba(255, 255, 255, 0.03) 180deg,
                        transparent 270deg,
                        rgba(255, 255, 255, 0.05) 360deg
                      )
                    `,
                  boxShadow: activeWorkspace === 'chat'
                    ? `
                      0 12px 40px rgba(147, 51, 234, 0.25),
                      0 6px 20px rgba(139, 92, 246, 0.15),
                      0 3px 10px rgba(0,0,0,0.2),
                      inset 0 3px 6px rgba(255,255,255,0.4),
                      inset 0 -3px 6px rgba(147, 51, 234, 0.1),
                      inset 1px 1px 2px rgba(255,255,255,0.3),
                      inset -1px -1px 2px rgba(0,0,0,0.1),
                      0 0 30px rgba(147, 51, 234, 0.2),
                      0 0 60px rgba(139, 92, 246, 0.08)
                    `
                    : `
                      0 8px 32px rgba(0,0,0,0.15),
                      0 4px 16px rgba(0,0,0,0.1),
                      0 2px 8px rgba(0,0,0,0.08),
                      inset 0 3px 6px rgba(255,255,255,0.35),
                      inset 0 -3px 6px rgba(0,0,0,0.05),
                      inset 1px 1px 2px rgba(255,255,255,0.25),
                      inset -1px -1px 2px rgba(0,0,0,0.08),
                      0 0 20px rgba(255,255,255,0.1)
                    `,
                  backdropFilter: 'blur(20px) saturate(1.4) brightness(1.2) contrast(1.1)',
                  borderRadius: '50%',
                  border: 'none',
                  outline: activeWorkspace === 'chat'
                    ? '1px solid rgba(147, 51, 234, 0.3)'
                    : '1px solid rgba(255, 255, 255, 0.2)'
                }}
                whileHover={{
                  scale: 1.1,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { type: "spring", stiffness: 600, damping: 30 }
                }}
              >
                <div className="w-6 h-6 relative mx-auto">
                  <Image
                    src="/images/icons/new_chat.png"
                    alt="New Chat"
                    width={24}
                    height={24}
                    className="object-contain opacity-90 hover:opacity-100 transition-opacity"
                  />
                </div>
              </motion.button>
            </DarkTooltip>

            <DarkTooltip content="Clear current chat" position="left" forcePosition={true}>
              <motion.button
                onClick={() => clearCurrentChat()}
                className="w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300"
                style={{
                  background: `
                    radial-gradient(ellipse 120% 80% at 25% 25%, rgba(239, 68, 68, 0.3) 0%, transparent 65%),
                    radial-gradient(ellipse 100% 120% at 75% 25%, rgba(220, 38, 38, 0.25) 0%, transparent 60%),
                    radial-gradient(ellipse 90% 110% at 25% 75%, rgba(248, 113, 113, 0.2) 0%, transparent 55%),
                    radial-gradient(ellipse 110% 90% at 75% 75%, rgba(185, 28, 28, 0.18) 0%, transparent 50%),
                    linear-gradient(135deg,
                      rgba(255, 255, 255, 0.25) 0%,
                      rgba(255, 255, 255, 0.15) 25%,
                      rgba(255, 255, 255, 0.08) 50%,
                      rgba(255, 255, 255, 0.18) 75%,
                      rgba(255, 255, 255, 0.22) 100%
                    ),
                    conic-gradient(from 45deg at 50% 50%,
                      rgba(239, 68, 68, 0.05) 0deg,
                      transparent 90deg,
                      rgba(220, 38, 38, 0.03) 180deg,
                      transparent 270deg,
                      rgba(239, 68, 68, 0.05) 360deg
                    )
                  `,
                  boxShadow: `
                    0 8px 32px rgba(239, 68, 68, 0.12),
                    0 4px 16px rgba(0,0,0,0.1),
                    0 2px 8px rgba(0,0,0,0.08),
                    inset 0 3px 6px rgba(255,255,255,0.35),
                    inset 0 -3px 6px rgba(239, 68, 68, 0.05),
                    inset 1px 1px 2px rgba(255,255,255,0.25),
                    inset -1px -1px 2px rgba(0,0,0,0.08),
                    0 0 20px rgba(239, 68, 68, 0.08)
                  `,
                  backdropFilter: 'blur(20px) saturate(1.4) brightness(1.2) contrast(1.1)',
                  borderRadius: '50%',
                  border: 'none',
                  outline: '1px solid rgba(239, 68, 68, 0.2)'
                }}
                whileHover={{
                  scale: 1.1,
                  style: {
                    background: `
                      radial-gradient(ellipse 120% 80% at 25% 25%, rgba(239, 68, 68, 0.4) 0%, transparent 65%),
                      radial-gradient(ellipse 100% 120% at 75% 25%, rgba(220, 38, 38, 0.35) 0%, transparent 60%),
                      radial-gradient(ellipse 90% 110% at 25% 75%, rgba(248, 113, 113, 0.3) 0%, transparent 55%),
                      radial-gradient(ellipse 110% 90% at 75% 75%, rgba(185, 28, 28, 0.25) 0%, transparent 50%),
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 0.35) 0%,
                        rgba(255, 255, 255, 0.25) 25%,
                        rgba(255, 255, 255, 0.15) 50%,
                        rgba(255, 255, 255, 0.28) 75%,
                        rgba(255, 255, 255, 0.32) 100%
                      ),
                      conic-gradient(from 45deg at 50% 50%,
                        rgba(239, 68, 68, 0.08) 0deg,
                        transparent 90deg,
                        rgba(220, 38, 38, 0.06) 180deg,
                        transparent 270deg,
                        rgba(239, 68, 68, 0.08) 360deg
                      )
                    `,
                    boxShadow: `
                      0 12px 40px rgba(239, 68, 68, 0.18),
                      0 6px 20px rgba(220, 38, 38, 0.12),
                      0 3px 10px rgba(0,0,0,0.15),
                      inset 0 3px 6px rgba(255,255,255,0.4),
                      inset 0 -3px 6px rgba(239, 68, 68, 0.08),
                      inset 1px 1px 2px rgba(255,255,255,0.3),
                      inset -1px -1px 2px rgba(0,0,0,0.1),
                      0 0 30px rgba(239, 68, 68, 0.15)
                    `,
                    outline: '1px solid rgba(239, 68, 68, 0.3)'
                  },
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { type: "spring", stiffness: 600, damping: 30 }
                }}
              >
                <div className="w-6 h-6 relative mx-auto">
                  <Image
                    src="/images/icons/bin.png"
                    alt="Clear"
                    width={24}
                    height={24}
                    className="object-contain opacity-90 hover:opacity-100 transition-opacity"
                  />
                </div>
              </motion.button>
            </DarkTooltip>
          </div>

          <div
            className="w-full h-full backdrop-blur-2xl rounded-3xl flex flex-col"
            style={{
              // Natural bluish-purplish-grey tint for readability and wallpaper compatibility
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 50%, rgba(255,255,255,0.12) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.20) 0%, rgba(139, 92, 246, 0.16) 30%, rgba(120, 120, 140, 0.14) 60%, rgba(59, 130, 246, 0.12) 100%)
              `,
              // Smooth liquid border
              border: '1px solid rgba(255,255,255,0.3)',
              // Soft flowing shadows
              boxShadow: `
                0 32px 64px rgba(0,0,0,0.4),
                0 16px 32px rgba(0,0,0,0.3),
                0 8px 16px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.4),
                inset 0 -1px 0 rgba(0,0,0,0.1),
                0 0 20px rgba(139,69,255,0.2)
              `,
              // Enhanced smooth glass
              backdropFilter: 'blur(30px) saturate(1.4) brightness(1.1)',
              WebkitBackdropFilter: 'blur(30px) saturate(1.4) brightness(1.1)',
              isolation: 'isolate',
              willChange: 'transform',
              WebkitFontSmoothing: 'antialiased',
              MozOsxFontSmoothing: 'grayscale',
              // Apple 26 rounded corners
              borderRadius: '24px'
            }}
          >
            {renderHeader()}
            {renderActiveWorkspace()}
            {activeWorkspace === 'chat' && renderInput()}
          </div>
        </motion.div>
      </AnimatePresence>
    )
  }

  // Window Mode
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        className="fixed inset-4 z-50 flex items-center justify-center"
      >
        <div className="relative">
          {/* Floating Bubble Control Buttons for Window Mode - Outside Frame */}
          <div className="absolute -left-20 top-4 flex flex-col space-y-3 z-10">
            <DarkTooltip content="View chat history">
              <motion.button
                onClick={() => setActiveWorkspace('history')}
                className={`w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300 border-2 ${
                  activeWorkspace === 'history'
                    ? 'border-purple-400/60'
                    : 'border-white/30'
                }`}
                style={{
                  borderRadius: '50%',
                  ...generateIconBackground({
                    variant: 'purple',
                    intensity: 1.3,
                    size: 'medium',
                    isActive: activeWorkspace === 'history',
                    isHovered: false
                  })
                }}
                whileHover={{
                  scale: 1.1,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { type: "spring", stiffness: 600, damping: 30 }
                }}
              >
                <div className="w-6 h-6 relative mx-auto">
                  <Image
                    src="/images/icons/history.png"
                    alt="History"
                    width={24}
                    height={24}
                    className="object-contain opacity-90 hover:opacity-100 transition-opacity"
                  />
                </div>
              </motion.button>
            </DarkTooltip>
            <DarkTooltip content="Start new chat">
              <motion.button
                onClick={() => {
                  startNewChat()
                  setActiveWorkspace('chat')
                }}
                className={`w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300 border-2 ${
                  activeWorkspace === 'chat'
                    ? 'border-purple-400/60'
                    : 'border-white/30'
                }`}
                style={{
                  borderRadius: '50%',
                  isolation: 'isolate',
                  willChange: 'transform',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                  ...generateIconBackground({
                    variant: 'purple',
                    intensity: 1.3,
                    size: 'medium',
                    isActive: activeWorkspace === 'chat',
                    isHovered: false
                  })
                }}
                whileHover={{
                  scale: 1.1,
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { type: "spring", stiffness: 600, damping: 30 }
                }}
              >
                <div className="w-6 h-6 relative mx-auto">
                  <Image
                    src="/images/icons/new_chat.png"
                    alt="New Chat"
                    width={24}
                    height={24}
                    className="object-contain opacity-90 hover:opacity-100 transition-opacity"
                  />
                </div>
              </motion.button>
            </DarkTooltip>

            <DarkTooltip content="Clear current chat">
              <motion.button
                onClick={() => clearCurrentChat()}
                className="w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300"
                style={{
                  borderRadius: '50%',
                  isolation: 'isolate',
                  willChange: 'transform',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                  // Custom red variant for delete button
                  background: `
                    radial-gradient(circle at center,
                      rgba(255,255,255,0.15) 0%,
                      rgba(255,255,255,0.08) 40%,
                      rgba(255,255,255,0.05) 80%,
                      rgba(255,255,255,0.02) 100%
                    ),
                    radial-gradient(circle at 30% 30%, rgba(239, 68, 68, 0.15) 0%, transparent 60%),
                    radial-gradient(circle at 70% 70%, rgba(220, 38, 38, 0.12) 0%, transparent 50%),
                    linear-gradient(135deg,
                      rgba(255,255,255,0.20) 0%,
                      rgba(255,255,255,0.05) 100%
                    )
                  `,
                  border: '1px solid rgba(239, 68, 68, 0.25)',
                  borderTop: '1px solid rgba(239, 68, 68, 0.35)',
                  borderLeft: '1px solid rgba(239, 68, 68, 0.30)',
                  borderBottom: '1px solid rgba(239, 68, 68, 0.15)',
                  borderRight: '1px solid rgba(239, 68, 68, 0.20)',
                  boxShadow: `
                    0 6px 16px rgba(239, 68, 68, 0.15),
                    0 3px 8px rgba(0,0,0,0.15),
                    inset 0 1px 0 rgba(255,255,255,0.30),
                    inset 0 -1px 0 rgba(0,0,0,0.10),
                    0 0 20px rgba(239, 68, 68, 0.12)
                  `,
                  backdropFilter: 'blur(15px) saturate(1.3) brightness(1.1)'
                }}
                whileHover={{
                  scale: 1.1,
                  style: {
                    background: `
                      radial-gradient(ellipse 120% 80% at 25% 25%, rgba(239, 68, 68, 0.4) 0%, transparent 65%),
                      radial-gradient(ellipse 100% 120% at 75% 25%, rgba(220, 38, 38, 0.35) 0%, transparent 60%),
                      radial-gradient(ellipse 90% 110% at 25% 75%, rgba(248, 113, 113, 0.3) 0%, transparent 55%),
                      radial-gradient(ellipse 110% 90% at 75% 75%, rgba(185, 28, 28, 0.25) 0%, transparent 50%),
                      linear-gradient(135deg,
                        rgba(255, 255, 255, 0.35) 0%,
                        rgba(255, 255, 255, 0.25) 25%,
                        rgba(255, 255, 255, 0.15) 50%,
                        rgba(255, 255, 255, 0.28) 75%,
                        rgba(255, 255, 255, 0.32) 100%
                      ),
                      conic-gradient(from 45deg at 50% 50%,
                        rgba(239, 68, 68, 0.08) 0deg,
                        transparent 90deg,
                        rgba(220, 38, 38, 0.06) 180deg,
                        transparent 270deg,
                        rgba(239, 68, 68, 0.08) 360deg
                      )
                    `,
                    boxShadow: `
                      0 12px 40px rgba(239, 68, 68, 0.18),
                      0 6px 20px rgba(220, 38, 38, 0.12),
                      0 3px 10px rgba(0,0,0,0.15),
                      inset 0 3px 6px rgba(255,255,255,0.4),
                      inset 0 -3px 6px rgba(239, 68, 68, 0.08),
                      inset 1px 1px 2px rgba(255,255,255,0.3),
                      inset -1px -1px 2px rgba(0,0,0,0.1),
                      0 0 30px rgba(239, 68, 68, 0.15)
                    `,
                    outline: '1px solid rgba(239, 68, 68, 0.3)'
                  },
                  transition: { type: "spring", stiffness: 400, damping: 25 }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { type: "spring", stiffness: 600, damping: 30 }
                }}
              >
                <div className="w-6 h-6 relative mx-auto">
                  <Image
                    src="/images/icons/bin.png"
                    alt="Clear"
                    width={24}
                    height={24}
                    className="object-contain opacity-90 hover:opacity-100 transition-opacity"
                  />
                </div>
              </motion.button>
            </DarkTooltip>
          </div>

          <div
            className="backdrop-blur-2xl rounded-2xl w-full max-w-2xl h-full max-h-[600px] flex flex-col"
            style={{
              // Natural bluish-purplish-grey tint for readability and wallpaper compatibility
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 50%, rgba(255,255,255,0.12) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.20) 0%, rgba(139, 92, 246, 0.16) 30%, rgba(120, 120, 140, 0.14) 60%, rgba(59, 130, 246, 0.12) 100%)
              `,
              // Smooth liquid border
              border: '1px solid rgba(255,255,255,0.3)',
              // Soft flowing shadows
              boxShadow: `
                0 32px 64px rgba(0,0,0,0.4),
                0 16px 32px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.4),
                inset 0 -1px 0 rgba(0,0,0,0.1),
                0 0 20px rgba(139,69,255,0.2)
              `,
              // Enhanced smooth glass
              backdropFilter: 'blur(30px) saturate(1.4) brightness(1.1)',
              WebkitBackdropFilter: 'blur(30px) saturate(1.4) brightness(1.1)',
              isolation: 'isolate',
              willChange: 'transform',
              WebkitFontSmoothing: 'antialiased',
              MozOsxFontSmoothing: 'grayscale',
              // Apple 26 rounded corners
              borderRadius: '16px'
            }}
          >
            {renderHeader()}
            {renderActiveWorkspace()}
            {activeWorkspace === 'chat' && renderInput()}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
