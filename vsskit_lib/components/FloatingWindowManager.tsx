'use client'

import React from 'react'
import { useFloatingWindowStore } from '@/lib/stores/floating-window-store'
import { FloatingWindow } from './FloatingWindow'
import { ZiExplorerContent, ZiWidgetsContent } from './FloatingWindowContent'

export function FloatingWindowManager() {
  const { windows, closeWindow, minimizeWindow, updateWindowPosition, updateWindowSize, bringToFront } = useFloatingWindowStore()

  const renderWindowContent = (contentType: string) => {
    switch (contentType) {
      case 'ziexplorer':
        return <ZiExplorerContent />
      case 'ziwidgets':
        return <ZiWidgetsContent />
      default:
        return <div className="text-white">Unknown content type</div>
    }
  }

  return (
    <>
      {windows.map((window) => (
        <FloatingWindow
          key={window.id}
          title={window.title}
          isOpen={window.isOpen && !window.isMinimized}
          onClose={() => closeWindow(window.id)}
          onMinimize={() => minimizeWindow(window.id)}
          defaultPosition={window.position}
          defaultSize={window.size}
          windowType="panel"
          className="z-50"
        >
          {renderWindowContent(window.content)}
        </FloatingWindow>
      ))}
      
      {/* Custom styles for scrollbars */}
      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, rgba(139,69,255,0.6) 0%, rgba(59,130,246,0.6) 100%);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, rgba(139,69,255,0.8) 0%, rgba(59,130,246,0.8) 100%);
        }
      `}</style>
    </>
  )
}
