'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useAppStore } from '@/lib/stores/app-store'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import { LiquidGlassPanel } from '@/components/LiquidGlass'
import { useLiquidGlass } from '@/hooks/useLiquidGlass'

interface DesktopItem {
  id: string
  name: string
  type: 'folder' | 'file' | 'app'
  icon: string
  position: { x: number; y: number }
  path?: string
  appName?: string
}

interface AppleDesktopEnvironmentProps {
  wallpaper: string
  onWallpaperChange: (wallpaper: string) => void
  isInitialized: boolean
  isMobile?: boolean
  isLowEndDevice?: boolean
}

export function AppleDesktopEnvironment({
  wallpaper,
  onWallpaperChange,
  isInitialized,
  isMobile = false,
  isLowEndDevice = false
}: AppleDesktopEnvironmentProps) {
  const { windows, addWindow, currentView } = useAppStore()
  const [desktopItems, setDesktopItems] = useState<DesktopItem[]>([])
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [draggedItem, setDraggedItem] = useState<string | null>(null)
  const desktopRef = React.useRef<HTMLDivElement>(null)
  const { generateIconBackground } = useLiquidGlass()

  // Initialize desktop items when workspace loads
  useEffect(() => {
    if (isInitialized) {
      loadDesktopItems()
    }
  }, [isInitialized])

  const loadDesktopItems = () => {
    const defaultItems: DesktopItem[] = [
      // Recycle Bin - Outside frames
      {
        id: 'recycle-bin',
        name: 'Recycle Bin',
        type: 'folder',
        icon: '/images/icons/empty-recycle-bin.png',
        position: { x: 50, y: window.innerHeight - 150 },
        path: '/RecycleBin'
      }
    ]
    setDesktopItems(defaultItems)
  }

  const handleItemDoubleClick = (item: DesktopItem) => {
    if (item.type === 'app' && item.appName) {
      launchApplication(item.appName)
    } else if (item.type === 'folder' && item.path) {
      openFolder(item.path)
    }
  }

  const launchApplication = (appName: string) => {
    const appConfigs = {
      'file-manager': { title: 'File Manager', size: { width: 800, height: 600 } },
      'pdf-viewer': { title: 'PDF Viewer', size: { width: 900, height: 700 } },
      'music-player': { title: 'Music Player', size: { width: 600, height: 400 } },
      'image-viewer': { title: 'Image Viewer', size: { width: 800, height: 600 } }
    }

    const config = appConfigs[appName as keyof typeof appConfigs] || {
      title: appName,
      size: { width: 600, height: 400 }
    }

    addWindow({
      title: config.title,
      appName: appName,
      position: { 
        x: Math.random() * 200 + 100, 
        y: Math.random() * 200 + 100 
      },
      size: config.size,
      isMinimized: false,
      isMaximized: false
    })
  }

  const openFolder = (path: string) => {
    launchApplication('file-manager')
  }

  const handleDesktopClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setSelectedItems([])
    }
  }

  return (
    <div
      ref={desktopRef}
      className={`absolute inset-0 w-full h-full overflow-hidden ${isMobile ? 'mobile-desktop' : 'desktop-desktop'}`}
      onClick={handleDesktopClick}
      style={{
        // Remove background to allow global wallpaper to show through
        backgroundColor: 'transparent'
      }}
    >
      {/* Main Folders Frame - Enhanced Liquid Glass - Hidden on mobile for performance */}
      {!isMobile && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{
            duration: isLowEndDevice ? 0.3 : 0.6,
            ease: "easeOut"
          }}
          className="absolute top-24 left-12 w-80 h-96"
        >
        <LiquidGlassPanel
          className="w-full h-full rounded-3xl p-6"
          tint="rgba(99, 102, 241, 0.26)"
          intensity={1.2}
          style={{
            background: `
              linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
              linear-gradient(135deg, rgba(99, 102, 241, 0.32) 0%, rgba(139, 92, 246, 0.28) 25%, rgba(110, 140, 160, 0.26) 50%, rgba(90, 150, 140, 0.24) 75%, rgba(59, 130, 246, 0.22) 100%)
            `
          }}
        >
        <h3 className="text-white text-lg font-semibold mb-6 text-center drop-shadow-lg">ZiExplorer</h3>
        
        <div className="space-y-4">
          {/* Documents Folder */}
          <motion.div
            whileHover={{ scale: 1.02, x: 5 }}
            whileTap={{ scale: 0.98 }}
            className="flex items-center space-x-4 p-4 rounded-2xl cursor-pointer transition-all duration-200"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
              border: '1px solid rgba(255,255,255,0.15)',
              boxShadow: '0 4px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
            }}
            onClick={() => openFolder('/Documents')}
          >
            <div
              className="w-12 h-12 relative rounded-xl flex items-center justify-center"
              style={generateIconBackground({
                variant: 'blue',
                intensity: 1.0,
                size: 'small',
                isActive: false,
                isHovered: false
              })}
            >
              <Image
                src="/images/icons/documents-folder.png"
                alt="Documents"
                width={32}
                height={32}
                className="object-contain"
              />
            </div>
            <span className="text-white font-medium drop-shadow-md">Documents</span>
          </motion.div>

          {/* Pictures Folder */}
          <motion.div
            whileHover={{ scale: 1.02, x: 5 }}
            whileTap={{ scale: 0.98 }}
            className="flex items-center space-x-4 p-4 rounded-2xl cursor-pointer transition-all duration-200"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
              border: '1px solid rgba(255,255,255,0.15)',
              boxShadow: '0 4px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
            }}
            onClick={() => openFolder('/Pictures')}
          >
            <div
              className="w-12 h-12 relative rounded-xl flex items-center justify-center"
              style={generateIconBackground({
                variant: 'cyan',
                intensity: 1.0,
                size: 'small',
                isActive: false,
                isHovered: false
              })}
            >
              <Image
                src="/images/icons/image-folder.png"
                alt="Pictures"
                width={32}
                height={32}
                className="object-contain"
              />
            </div>
            <span className="text-white font-medium drop-shadow-md">Pictures</span>
          </motion.div>

          {/* Music Folder */}
          <motion.div
            whileHover={{ scale: 1.02, x: 5 }}
            whileTap={{ scale: 0.98 }}
            className="flex items-center space-x-4 p-4 rounded-2xl cursor-pointer transition-all duration-200"
            style={{
              background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
              border: '1px solid rgba(255,255,255,0.15)',
              boxShadow: '0 4px 16px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.2)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)'
            }}
            onClick={() => openFolder('/Music')}
          >
            <div
              className="w-12 h-12 relative rounded-xl flex items-center justify-center"
              style={generateIconBackground({
                variant: 'purple',
                intensity: 1.0,
                size: 'small',
                isActive: false,
                isHovered: false
              })}
            >
              <Image
                src="/images/icons/music-folder.png"
                alt="Music"
                width={32}
                height={32}
                className="object-contain"
              />
            </div>
            <span className="text-white font-medium drop-shadow-md">Music</span>
          </motion.div>
        </div>
        </LiquidGlassPanel>
      </motion.div>
      )}

      {/* Recycle Bin - Outside frames - Optimized for mobile */}
      {desktopItems.map((item) => (
        <DesktopItem
          key={item.id}
          item={item}
          isSelected={selectedItems.includes(item.id)}
          onDoubleClick={() => handleItemDoubleClick(item)}
          onSelect={(id) => setSelectedItems([id])}
          isMobile={isMobile}
          isLowEndDevice={isLowEndDevice}
        />
      ))}
    </div>
  )
}

// Desktop Item Component
interface DesktopItemProps {
  item: DesktopItem
  isSelected: boolean
  onDoubleClick: () => void
  onSelect: (id: string) => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

function DesktopItem({
  item,
  isSelected,
  onDoubleClick,
  onSelect,
  isMobile = false,
  isLowEndDevice = false
}: DesktopItemProps) {
  const iconSize = isMobile ? 'w-24 h-24' : 'w-20 h-20'
  const imageSize = isMobile ? 56 : 48
  const touchTarget = isMobile ? 'min-h-[48px] min-w-[48px]' : ''

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      whileHover={!isLowEndDevice ? { scale: 1.05 } : {}}
      whileTap={{ scale: 0.95 }}
      className={`
        absolute flex flex-col items-center justify-center ${iconSize} p-2 rounded-2xl cursor-pointer
        transition-all ${isLowEndDevice ? 'duration-100' : 'duration-200'} select-none ${touchTarget}
        ${isSelected
          ? 'bg-blue-500/20 border-2 border-blue-400/50'
          : 'hover:bg-white/10'
        }
      `}
      style={{
        left: item.position.x,
        top: item.position.y
      }}
      onClick={() => onSelect(item.id)}
      onDoubleClick={onDoubleClick}
    >
      <div className={`${isMobile ? 'w-14 h-14' : 'w-12 h-12'} relative mb-1`}>
        <Image
          src={item.icon}
          alt={item.name}
          width={imageSize}
          height={imageSize}
          className="object-contain drop-shadow-lg"
        />
      </div>
      <span className={`text-white ${isMobile ? 'text-sm' : 'text-xs'} font-medium text-center leading-tight drop-shadow-md`}>
        {item.name}
      </span>
    </motion.div>
  )
}
