'use client'

import { useState, useEffect } from 'react'
import { useAppStore } from '@/lib/stores/app-store'
import { motion, AnimatePresence } from 'framer-motion'
import { DarkTooltip } from '@/components/DarkTooltip'
import Tooltip from '@/components/Tooltip'
import { SpotlightSearch } from '@/components/SpotlightSearch'
import { useLiquidGlass } from '@/hooks/useLiquidGlass'
import { CollapsedQuickAccess, CollapsedZiWidgets } from '@/components/CollapsedPanels'
import Image from 'next/image'

interface AppIcon {
  id: string
  name: string
  icon: string
  isRunning: boolean
  isActive: boolean
  type: 'ai' | 'app'
}

interface HarmonyTaskbarProps {
  showZiAssist?: boolean
  onZiAssistToggle?: () => void
  isMobile?: boolean
  isLowEndDevice?: boolean
}

export function HarmonyTaskbar({
  showZiAssist = false,
  onZiAssistToggle,
  isMobile = false,
  isLowEndDevice = false
}: HarmonyTaskbarProps) {
  const { windows, addWindow, focusWindow, minimizeWindow } = useAppStore()
  const [apps, setApps] = useState<AppIcon[]>([])
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showSpotlightSearch, setShowSpotlightSearch] = useState(false)
  const { generateIconBackground } = useLiquidGlass()

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // Initialize apps when component mounts
  useEffect(() => {
    initializeApps()
  }, [])

  // Update running apps based on windows
  useEffect(() => {
    updateRunningApps()
  }, [windows])

  const initializeApps = () => {
    const defaultApps: AppIcon[] = [
      // ZiAssist AI - First position (Harmony OS style)
      {
        id: 'ziassist',
        name: 'ZiAssist',
        icon: '/images/icons/ZiAssist.png',
        isRunning: false,
        isActive: false,
        type: 'ai'
      },
      // Grouped center apps
      {
        id: 'pdf-viewer',
        name: 'PDF Viewer',
        icon: '/images/icons/pdf_reader.png',
        isRunning: false,
        isActive: false,
        type: 'app'
      },
      {
        id: 'music-player',
        name: 'Music Player',
        icon: '/images/icons/music_player.png',
        isRunning: false,
        isActive: false,
        type: 'app'
      },
      {
        id: 'image-viewer',
        name: 'Image Viewer',
        icon: '/images/icons/image_viwer.png',
        isRunning: false,
        isActive: false,
        type: 'app'
      },
      {
        id: 'file-manager',
        name: 'File Manager',
        icon: '/images/icons/file_explorer.png',
        isRunning: false,
        isActive: false,
        type: 'app'
      }
    ]
    setApps(defaultApps)
  }

  const updateRunningApps = () => {
    setApps(prevApps =>
      prevApps.map(app => ({
        ...app,
        isRunning: windows.some(window => window.appName === app.id),
        isActive: windows.some(window => window.appName === app.id && !window.isMinimized)
      }))
    )
  }

  const handleAppClick = (appId: string) => {
    if (appId === 'ziassist') {
      onZiAssistToggle?.()
      return
    }

    const existingWindow = windows.find(window => window.appName === appId)
    
    if (existingWindow) {
      if (existingWindow.isMinimized) {
        // Restore minimized window
        focusWindow(existingWindow.id)
      } else {
        // Minimize active window
        minimizeWindow(existingWindow.id)
      }
      return
    }

    // Launch new app
    launchApplication(appId)
  }

  const launchApplication = (appId: string) => {
    const appConfigs = {
      'pdf-viewer': { title: 'PDF Viewer', size: { width: 900, height: 700 } },
      'music-player': { title: 'Music Player', size: { width: 600, height: 400 } },
      'image-viewer': { title: 'Image Viewer', size: { width: 800, height: 600 } },
      'file-manager': { title: 'File Manager', size: { width: 800, height: 600 } }
    }

    const config = appConfigs[appId as keyof typeof appConfigs] || {
      title: appId,
      size: { width: 600, height: 400 }
    }

    addWindow({
      title: config.title,
      appName: appId,
      position: { 
        x: Math.random() * 200 + 100, 
        y: Math.random() * 200 + 100 
      },
      size: config.size,
      isMinimized: false,
      isMaximized: false
    })
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], { 
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  const iconSize = isMobile ? 'w-12 h-12' : 'w-14 h-14'
  const imageSize = isMobile ? 32 : 40
  const spacing = isMobile ? 'space-x-2' : 'space-x-3'
  const padding = isMobile ? 'px-4 py-2' : 'px-6 py-3'
  const animationDuration = isLowEndDevice ? 0.2 : 0.5

  return (
    <>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: animationDuration, ease: "easeOut" }}
        className={`fixed bottom-0 left-0 right-0 z-50 ${isMobile ? 'mobile-taskbar' : ''}`}
      >
      {/* Harmony OS Style Taskbar - Mobile Optimized */}
      <div
        className={`mx-4 mb-4 backdrop-blur-2xl rounded-3xl harmony-taskbar ${isMobile ? 'mobile-optimized' : ''}`}
        style={{
          background: `
            linear-gradient(135deg,
              rgba(255,255,255,0.08) 0%,
              rgba(255,255,255,0.05) 30%,
              rgba(255,255,255,0.03) 60%,
              rgba(255,255,255,0.02) 100%
            ),
            linear-gradient(135deg,
              rgba(99, 102, 241, 0.32) 0%,
              rgba(139, 92, 246, 0.28) 25%,
              rgba(110, 140, 160, 0.26) 50%,
              rgba(90, 150, 140, 0.24) 75%,
              rgba(59, 130, 246, 0.22) 100%
            ),
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.04) 0%, transparent 50%),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.02) 0%, transparent 50%)
          `,
          backdropFilter: 'blur(20px) saturate(1.2) brightness(1.05)',
          WebkitBackdropFilter: 'blur(20px) saturate(1.2) brightness(1.05)',
          border: '1px solid rgba(255,255,255,0.15)',
          borderTop: '1px solid rgba(255,255,255,0.2)',
          borderLeft: '1px solid rgba(255,255,255,0.18)',
          borderBottom: '0.5px solid rgba(255,255,255,0.08)',
          borderRight: '0.5px solid rgba(255,255,255,0.1)',
          boxShadow: `
            0 24px 48px rgba(0,0,0,0.35),
            0 12px 24px rgba(0,0,0,0.25),
            0 6px 12px rgba(0,0,0,0.15),
            0 2px 6px rgba(0,0,0,0.1),
            inset 0 1px 2px rgba(255,255,255,0.25),
            inset 0 0.5px 1px rgba(255,255,255,0.15),
            inset 0 -0.5px 1px rgba(0,0,0,0.06),
            inset 1px 0 2px rgba(255,255,255,0.12),
            inset -1px 0 2px rgba(0,0,0,0.04),
            0 0 0 0.5px rgba(255,255,255,0.06)
          `,
          transform: 'perspective(2000px) rotateX(1deg)',
          transformStyle: 'preserve-3d',
          filter: 'brightness(1.002) saturate(1.005)',
          position: 'relative'
        }}
      >
        <div className={`flex items-center justify-between ${padding} overflow-visible`}>

          {/* Left Section - ZiAssist AI and Search */}
          <div className={`flex items-center ${spacing} overflow-visible`}>
            <div className="relative">
              <DarkTooltip content="ZiAssist AI - Your Study Companion">
                <motion.button
                  whileHover={{ scale: 1.25, y: -5 }}
                  whileTap={{ scale: 0.88 }}
                  onClick={() => handleAppClick('ziassist')}
                  className={`relative ${iconSize} rounded-2xl flex items-center justify-center transition-all ${isLowEndDevice ? 'duration-150' : 'duration-300'} ease-out overflow-visible ${isMobile ? 'min-h-[48px] min-w-[48px]' : ''}`}
                  style={{
                    ...generateIconBackground({
                      variant: 'purple',
                      intensity: 1.4,
                      size: 'medium',
                      isActive: showZiAssist,
                      isHovered: false
                    }),
                    backdropFilter: 'blur(20px) saturate(1.8) brightness(1.15)',
                    WebkitBackdropFilter: 'blur(20px) saturate(1.8) brightness(1.15)'
                  }}
                >
                <div className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} relative`}>
                  <Image
                    src="/images/icons/ZiAssist.png"
                    alt="ZiAssist"
                    width={imageSize}
                    height={imageSize}
                    className="object-contain"
                  />
                </div>
                </motion.button>
              </DarkTooltip>

              {/* ZiAssist Selection Pill - Inside Container, Outside Icon */}
              <AnimatePresence>
                {showZiAssist && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="absolute"
                    style={{
                      width: '24px',
                      height: '4px',
                      borderRadius: '2px',
                      bottom: '-8px',
                      left: '50%',
                      marginLeft: '-12px', // Half of width (24px/2) for perfect centering
                      zIndex: 1000,
                      background: `
                        linear-gradient(135deg,
                          rgba(147, 51, 234, 0.9) 0%,
                          rgba(147, 51, 234, 1) 30%,
                          rgba(147, 51, 234, 1) 70%,
                          rgba(147, 51, 234, 0.9) 100%
                        ),
                        linear-gradient(90deg,
                          rgba(255, 255, 255, 0.2) 0%,
                          rgba(255, 255, 255, 0.1) 50%,
                          rgba(255, 255, 255, 0.2) 100%
                        )
                      `,
                      border: '0.5px solid rgba(255, 255, 255, 0.3)',
                      boxShadow: `
                        0 0 8px rgba(147, 51, 234, 0.8),
                        0 0 16px rgba(147, 51, 234, 0.6),
                        0 0 24px rgba(147, 51, 234, 0.4),
                        inset 0 0.5px 1px rgba(255, 255, 255, 0.4),
                        inset 0 -0.5px 1px rgba(0, 0, 0, 0.2),
                        0 1px 2px rgba(0, 0, 0, 0.3)
                      `,
                      filter: 'brightness(1.2) saturate(1.3)',
                      transformOrigin: 'center'
                    }}
                  />
                )}
              </AnimatePresence>

              {/* ZiAssist Notification Dot - Top Corner of Icon */}
              <AnimatePresence>
                {showZiAssist && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="absolute w-3 h-3 rounded-full"
                    style={{
                      top: '-2px',
                      right: '-2px',
                      zIndex: 1000,
                      background: `
                        radial-gradient(circle at center,
                          rgba(34, 197, 94, 1) 0%,
                          rgba(34, 197, 94, 0.9) 30%,
                          rgba(16, 185, 129, 0.8) 70%,
                          rgba(34, 197, 94, 0.6) 100%
                        )
                      `,
                      border: '1.5px solid rgba(255, 255, 255, 0.4)',
                      boxShadow: `
                        0 0 12px rgba(34, 197, 94, 0.9),
                        0 0 24px rgba(34, 197, 94, 0.7),
                        0 0 36px rgba(34, 197, 94, 0.5),
                        inset 0 0.5px 1px rgba(255, 255, 255, 0.6),
                        inset 0 -0.5px 1px rgba(0, 0, 0, 0.2),
                        0 1px 3px rgba(0, 0, 0, 0.3)
                      `,
                      filter: 'brightness(1.3) saturate(1.4)',
                      transformOrigin: 'center'
                    }}
                  />
                )}
              </AnimatePresence>
            </div>

            {/* Search Launcher */}
            <div className="relative">
              <DarkTooltip content="Spotlight Search - Find anything instantly">
                <motion.button
                  whileHover={{ scale: 1.25, y: -5 }}
                  whileTap={{ scale: 0.88 }}
                  onClick={() => setShowSpotlightSearch(true)}
                  className={`relative ${iconSize} rounded-2xl flex items-center justify-center transition-all ${isLowEndDevice ? 'duration-150' : 'duration-300'} ease-out overflow-visible ${isMobile ? 'min-h-[48px] min-w-[48px]' : ''}`}
                  style={{
                    ...generateIconBackground({
                      variant: 'green',
                      intensity: 1.4,
                      size: 'medium',
                      isActive: showSpotlightSearch,
                      isHovered: false
                    }),
                    backdropFilter: 'blur(20px) saturate(1.8) brightness(1.15)',
                    WebkitBackdropFilter: 'blur(20px) saturate(1.8) brightness(1.15)'
                  }}
                >
                <div className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} relative`}>
                  <Image
                    src="/images/icons/search.png"
                    alt="Search"
                    width={imageSize}
                    height={imageSize}
                    className="object-contain"
                  />
                </div>
                </motion.button>
              </DarkTooltip>


            </div>

            {/* Collapsed Quick Access - After ZiAssist and Search */}
            <CollapsedQuickAccess onAppLaunch={handleAppClick} />
          </div>

          {/* Center Section - App Dock - Mobile Optimized with Proper Layering */}
          <div
            className={`relative rounded-2xl ${padding} ${isMobile ? 'max-w-[60vw]' : ''}`}
            style={{
              background: `
                linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%),
                linear-gradient(135deg, rgba(99, 102, 241, 0.30) 0%, rgba(139, 92, 246, 0.26) 25%, rgba(110, 140, 160, 0.24) 50%, rgba(90, 150, 140, 0.22) 75%, rgba(59, 130, 246, 0.20) 100%)
              `,
              border: '1px solid rgba(255,255,255,0.15)',
              boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.2), 0 4px 8px rgba(0,0,0,0.1)',
              overflow: 'visible' // Allow icons to scale outside
            }}
          >
            {/* Icon Container Layer - Above Background */}
            <div
              className={`flex items-center ${spacing} ${isMobile ? 'flex-wrap' : ''}`}
              style={{
                overflow: 'visible', // Critical: Allow icons to scale outside
                zIndex: 10 // Above background
              }}
            >
            <AnimatePresence>
              {apps.filter(app => app.type === 'app').map((app) => (
                <div key={app.id} className="relative">
                  <DarkTooltip content={app.name}>
                    <motion.button
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      whileHover={{ scale: 1.25, y: -6 }}
                      whileTap={{ scale: 0.88 }}
                      onClick={() => handleAppClick(app.id)}
                      className={`relative ${iconSize} rounded-2xl flex items-center justify-center transition-all ${isLowEndDevice ? 'duration-150' : 'duration-300'} ease-out overflow-visible ${isMobile ? 'min-h-[48px] min-w-[48px]' : ''}`}
                      style={{
                        ...generateIconBackground({
                          variant: app.id === 'pdf-viewer' ? 'orange' :
                                   app.id === 'music-player' ? 'pink' :
                                   app.id === 'image-viewer' ? 'cyan' : 'blue',
                          intensity: 1.4,
                          size: 'medium',
                          isActive: app.isActive,
                          isHovered: false
                        }),
                        backdropFilter: 'blur(20px) saturate(1.8) brightness(1.15)',
                        WebkitBackdropFilter: 'blur(20px) saturate(1.8) brightness(1.15)'
                      }}
                    >
                    <div className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} relative`}>
                      <Image
                        src={app.icon}
                        alt={app.name}
                        width={imageSize}
                        height={imageSize}
                        className="object-contain"
                      />
                    </div>


                    </motion.button>
                  </DarkTooltip>

                  {/* App Selection Pill - Inside Middle Container, Outside Icon */}
                  <AnimatePresence>
                    {app.isActive && (
                      <motion.div
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0, opacity: 0 }}
                        className="absolute"
                        style={{
                          width: '24px',
                          height: '4px',
                          borderRadius: '2px',
                          bottom: '-8px',
                          left: '50%',
                          marginLeft: '-12px', // Half of width (24px/2) for perfect centering
                          zIndex: 1000,
                          background: `
                            linear-gradient(135deg,
                              rgba(34, 197, 94, 0.9) 0%,
                              rgba(34, 197, 94, 1) 30%,
                              rgba(34, 197, 94, 1) 70%,
                              rgba(34, 197, 94, 0.9) 100%
                            ),
                            linear-gradient(90deg,
                              rgba(255, 255, 255, 0.2) 0%,
                              rgba(255, 255, 255, 0.1) 50%,
                              rgba(255, 255, 255, 0.2) 100%
                            )
                          `,
                          border: '0.5px solid rgba(255, 255, 255, 0.3)',
                          boxShadow: `
                            0 0 8px rgba(34, 197, 94, 0.8),
                            0 0 16px rgba(34, 197, 94, 0.6),
                            0 0 24px rgba(34, 197, 94, 0.4),
                            inset 0 0.5px 1px rgba(255, 255, 255, 0.4),
                            inset 0 -0.5px 1px rgba(0, 0, 0, 0.2),
                            0 1px 2px rgba(0, 0, 0, 0.3)
                          `,
                          filter: 'brightness(1.2) saturate(1.3)',
                          transformOrigin: 'center'
                        }}
                      />
                    )}
                  </AnimatePresence>
                </div>
              ))}
            </AnimatePresence>
            </div>
          </div>

          {/* Collapsed ZiWidgets - Between Mid Panel and Time */}
          <CollapsedZiWidgets />

          {/* Right Section - Time Display - Hidden on small mobile screens */}
          {!isMobile && (
            <div className="flex items-center space-x-4">
              <Tooltip content={`${formatDate(currentTime)} ${formatTime(currentTime)}`} placement="top">
                <div className="text-white text-sm font-medium text-right cursor-default">
                  <div className="drop-shadow-lg">{formatTime(currentTime)}</div>
                  <div className="text-xs text-white/80 drop-shadow-md">{formatDate(currentTime)}</div>
                </div>
              </Tooltip>
            </div>
          )}
        </div>
      </div>

      </motion.div>



      {/* Spotlight Search Component */}
      <SpotlightSearch
        isOpen={showSpotlightSearch}
        onClose={() => setShowSpotlightSearch(false)}
      />
    </>
  )
}
