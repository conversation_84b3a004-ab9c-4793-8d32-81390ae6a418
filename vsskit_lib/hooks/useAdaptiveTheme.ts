'use client'

import { useState, useEffect } from 'react'
import { adaptiveThemeSystem, AdaptiveTheme } from '@/lib/adaptive-theme-system'

export function useAdaptiveTheme() {
  const [theme, setTheme] = useState<AdaptiveTheme | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    let unsubscribe: (() => void) | null = null

    const initializeTheme = async () => {
      try {
        // Get current theme or analyze wallpaper
        const currentTheme = adaptiveThemeSystem.getCurrentTheme()
        if (currentTheme) {
          setTheme(currentTheme)
          setIsLoading(false)
        } else {
          // Force initial analysis
          const newTheme = await adaptiveThemeSystem.forceThemeUpdate()
          setTheme(newTheme)
          setIsLoading(false)
        }

        // Subscribe to theme changes
        unsubscribe = adaptiveThemeSystem.onThemeChange((newTheme) => {
          console.log('🎨 Theme updated:', newTheme.mode, newTheme.colors.primary)
          setTheme(newTheme)
        })
      } catch (error) {
        console.error('🎨 Theme initialization failed:', error)
        setIsLoading(false)
      }
    }

    initializeTheme()

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [])

  const forceUpdate = async () => {
    setIsLoading(true)
    try {
      const newTheme = await adaptiveThemeSystem.forceThemeUpdate()
      setTheme(newTheme)
    } catch (error) {
      console.error('🎨 Force theme update failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return {
    theme,
    isLoading,
    forceUpdate,
    isDark: theme?.mode === 'dark',
    isLight: theme?.mode === 'light',
    colors: theme?.colors || null,
    wallpaperColors: theme?.wallpaperColors || null,
    tinting: theme?.tinting || null
  }
}

// Hook for getting theme-aware liquid glass styles
export function useAdaptiveLiquidGlassStyles(options: {
  intensity?: number
  size?: 'small' | 'medium' | 'large'
  variant?: 'default' | 'panel' | 'button' | 'window'
} = {}) {
  const { theme } = useAdaptiveTheme()
  const { intensity = 1, size = 'medium', variant = 'default' } = options

  if (!theme) {
    return {
      background: `
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
        linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.03) 100%)
      `,
      backdropFilter: 'blur(10px)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      color: 'rgb(255, 255, 255)'
    }
  }

  const sizeMultipliers = { small: 0.7, medium: 1, large: 1.3 }
  const multiplier = sizeMultipliers[size]
  const baseBlur = 12 * intensity * multiplier

  // Base adaptive styles
  const baseStyles = {
    color: theme.colors.text,
    textShadow: theme.mode === 'dark' 
      ? '0 1px 3px rgba(0, 0, 0, 0.8)' 
      : '0 1px 2px rgba(255, 255, 255, 0.8)',
    transition: 'all 300ms cubic-bezier(0.175, 0.885, 0.32, 2.2)'
  }

  // Variant-specific styles
  switch (variant) {
    case 'panel':
      return {
        ...baseStyles,
        background: `
          ${theme.tinting.overlay},
          linear-gradient(135deg, rgba(99, 102, 241, 0.06) 0%, rgba(139, 92, 246, 0.03) 100%),
          radial-gradient(circle at center, ${theme.colors.surface}80 0%, transparent 70%),
          ${theme.tinting.blend}
        `,
        backdropFilter: `blur(${baseBlur}px) saturate(1.4) brightness(1.1)`,
        border: `1px solid ${theme.colors.border}`,
        borderTop: `1px solid ${theme.colors.border}40`,
        boxShadow: `
          inset 0 1px 0 ${theme.colors.border}60,
          0 8px 32px ${theme.colors.shadow},
          0 0 20px ${theme.colors.glow}
        `
      }

    case 'button':
      return {
        ...baseStyles,
        background: `
          linear-gradient(135deg, rgba(99, 102, 241, 0.04) 0%, rgba(139, 92, 246, 0.02) 100%),
          radial-gradient(circle at center, ${theme.colors.primary}40 0%, transparent 70%),
          ${theme.tinting.blend}
        `,
        backdropFilter: `blur(${baseBlur * 0.8}px) saturate(1.2)`,
        border: `1px solid ${theme.colors.border}`,
        boxShadow: `
          inset 0 1px 0 ${theme.colors.border}80,
          0 4px 16px ${theme.colors.shadow}
        `,
        ':hover': {
          background: `
            radial-gradient(circle at center, ${theme.colors.primary}60 0%, transparent 70%),
            ${theme.tinting.blend}
          `,
          transform: 'translateY(-1px)',
          boxShadow: `
            inset 0 1px 0 ${theme.colors.border}80,
            0 6px 20px ${theme.colors.shadow}
          `
        }
      }

    case 'window':
      return {
        ...baseStyles,
        background: `
          ${theme.tinting.overlay},
          linear-gradient(135deg, rgba(99, 102, 241, 0.07) 0%, rgba(139, 92, 246, 0.04) 100%),
          radial-gradient(circle at 25% 25%, ${theme.colors.accent}20 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, ${theme.colors.secondary}15 0%, transparent 40%),
          linear-gradient(135deg, ${theme.colors.surface}90 0%, ${theme.colors.background}70 100%)
        `,
        backdropFilter: `blur(${baseBlur * 1.2}px) saturate(1.5) brightness(1.1)`,
        border: `1px solid ${theme.colors.border}`,
        borderTop: `1px solid ${theme.colors.border}80`,
        borderLeft: `1px solid ${theme.colors.border}60`,
        boxShadow: `
          inset 0 1px 0 ${theme.colors.border}80,
          inset 0 -1px 0 ${theme.colors.shadow}40,
          0 16px 64px ${theme.colors.shadow},
          0 0 40px ${theme.colors.glow}
        `
      }

    default:
      return {
        ...baseStyles,
        background: `
          linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.03) 100%),
          radial-gradient(circle at center, ${theme.colors.surface}60 0%, transparent 70%),
          ${theme.tinting.blend}
        `,
        backdropFilter: `blur(${baseBlur}px) saturate(1.3)`,
        border: `1px solid ${theme.colors.border}`,
        boxShadow: `
          inset 0 1px 0 ${theme.colors.border}60,
          0 8px 24px ${theme.colors.shadow}
        `
      }
  }
}

// Hook for theme-aware text colors
export function useAdaptiveTextColor() {
  const { theme } = useAdaptiveTheme()
  
  if (!theme) {
    return {
      primary: 'rgb(255, 255, 255)',
      secondary: 'rgb(200, 200, 200)',
      muted: 'rgb(150, 150, 150)'
    }
  }

  return {
    primary: theme.colors.text,
    secondary: theme.colors.textSecondary,
    muted: theme.mode === 'dark' ? 'rgb(120, 120, 120)' : 'rgb(100, 100, 100)',
    accent: theme.colors.accent,
    onSurface: theme.colors.text
  }
}
