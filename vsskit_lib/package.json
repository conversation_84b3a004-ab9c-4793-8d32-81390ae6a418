{"name": "kotobee-launcher-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@cubone/react-file-manager": "^1.24.0", "@hookform/resolvers": "^3.3.2", "@litecode-ide/virtual-file-system": "^1.1.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.17.0", "@types/react-resizable": "^3.0.8", "@use-gesture/react": "^10.3.1", "@xenova/transformers": "^2.17.2", "axios": "^1.6.2", "browserfs": "^1.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "colorthief": "^2.6.0", "fast-average-color": "^9.5.0", "firebase": "^11.9.1", "framer-motion": "^10.18.0", "lottie-react": "^2.4.1", "lucide-react": "^0.303.0", "memfs": "^4.17.2", "music-metadata-browser": "^2.5.11", "next": "14.0.4", "next-auth": "^4.24.5", "onnxruntime-web": "^1.22.0", "pdfjs-dist": "^5.3.31", "react": "^18", "react-background-slider": "^4.0.0", "react-big-calendar": "^1.19.2", "react-clock": "^6.0.0", "react-contexify": "^6.0.0", "react-contextmenu": "^2.14.0", "react-datetime-picker": "^6.0.1", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.2", "react-loader-spinner": "^6.1.6", "react-markdown-editor-lite": "^1.3.4", "react-photo-gallery": "^8.0.0", "react-resizable": "^3.0.5", "react-responsive": "^10.0.1", "react-slideshow-image": "^4.3.2", "react-spinners": "^0.17.0", "react-splash-screen": "^0.0.3", "react-spring": "^10.0.1", "react-touch-events": "^3.0.0", "react-widgets": "^5.8.6", "react-winbox": "^1.5.0", "styled-components": "^6.1.18", "supabase": "^2.24.3", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "use-background-image": "^0.1.2", "wavesurfer.js": "^7.9.5", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dnd": "^3.0.2", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "ignore-loader": "^0.1.2", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}